import '../models_qwer_Kipra_84/models_export_Kipra.dart';
import '../services_qwer_Kipra_45/data_persistence_service_mnbv_Kipra_78.dart';
import 'enhanced_sample_data_hjkl_Kipra_92.dart';

/// Updated sample data class for Kipra volleyball community
/// Now uses enhanced data and persistence service
class SampleData_Kipra {
  /// Get sample users (now uses persistence service)
  static Future<List<UserModel_Kipra>> getSampleUsers_Kipra() async {
    return await DataPersistenceService_Kipra.loadUsers_Kipra();
  }

  /// Get sample users synchronously (fallback to enhanced sample data)
  static List<UserModel_Kipra> getSampleUsersSync_Kipra() {
    return EnhancedSampleData_Kipra.getSampleUsers_Kipra();
  }

  /// Get sample posts (now uses persistence service)
  static Future<List<PostModel_Kipra>> getSamplePosts_Kipra() async {
    return await DataPersistenceService_Kipra.loadPosts_Kipra();
  }

  /// Get sample posts synchronously (fallback to enhanced sample data)
  static List<PostModel_Kipra> getSamplePostsSync_Kipra() {
    return EnhancedSampleData_Kipra.getSamplePosts_Kipra();
  }

  /// Get filtered posts for current user
  static Future<List<PostModel_Kipra>> getFilteredPosts_Kipra(
      [UserModel_Kipra? user]) async {
    if (user != null) {
      // If user is provided, filter based on that user's preferences
      final posts = await getSamplePosts_Kipra();
      return posts.where((post) {
        // Filter blocked users' posts
        if (user.blockedUserIds_Kipra.contains(post.authorId_Kipra)) {
          return false;
        }

        // Filter reported posts
        if (user.reportedPostIds_Kipra.contains(post.postId_Kipra)) {
          return false;
        }

        // Filter deleted posts
        if (post.isDeleted_Kipra) {
          return false;
        }

        return true;
      }).toList();
    } else {
      // Use persistence service's filtered posts method
      return await DataPersistenceService_Kipra.getFilteredPosts_Kipra();
    }
  }

  /// Get user by ID
  static Future<UserModel_Kipra?> getUserById_Kipra(String userId) async {
    return await DataPersistenceService_Kipra.getUserById_Kipra(userId);
  }

  /// Get post by ID
  static Future<PostModel_Kipra?> getPostById_Kipra(String postId) async {
    return await DataPersistenceService_Kipra.getPostById_Kipra(postId);
  }

  /// Get current user
  static Future<UserModel_Kipra?> getCurrentUser_Kipra() async {
    return await DataPersistenceService_Kipra.getCurrentUser_Kipra();
  }

  /// Get sample comments (extract from posts)
  static Future<List<CommentModel_Kipra>> getSampleComments_Kipra() async {
    final posts = await getSamplePosts_Kipra();
    final List<CommentModel_Kipra> allComments = [];

    for (final post in posts) {
      allComments.addAll(post.comments_Kipra);
    }

    return allComments;
  }

  /// Initialize data persistence service
  static Future<void> initializeData_Kipra() async {
    await DataPersistenceService_Kipra.initialize_Kipra();
  }

  /// Reset to sample data
  static Future<bool> resetToSampleData_Kipra() async {
    return await DataPersistenceService_Kipra.resetToSampleData_Kipra();
  }

  /// Update user
  static Future<bool> updateUser_Kipra(UserModel_Kipra user) async {
    return await DataPersistenceService_Kipra.updateUser_Kipra(user);
  }

  /// Update post
  static Future<bool> updatePost_Kipra(PostModel_Kipra post) async {
    return await DataPersistenceService_Kipra.updatePost_Kipra(post);
  }

  /// Add new post
  static Future<bool> addPost_Kipra(PostModel_Kipra post) async {
    return await DataPersistenceService_Kipra.addPost_Kipra(post);
  }
}
