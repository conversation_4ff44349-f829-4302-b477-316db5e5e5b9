import '../models_qwer_Kipra_84/models_export_Kipra.dart';
import 'dart:math';

/// Enhanced sample data class for Kipra volleyball community
/// Provides comprehensive mock data for development and testing
class EnhancedSampleData_Kipra {
  static final Random _random = Random();

  // Volleyball-themed tags
  static const List<String> _volleyballTags_Kipra = [
    'spike',
    'serve',
    'block',
    'dig',
    'set',
    'training',
    'technique',
    'tournament'
  ];

  // Video paths for posts
  static const List<String> _videoPaths_Kipra = [
    'assets_Kipra/videos/1.mp4',
    'assets_Kipra/videos/2.mp4',
    'assets_Kipra/videos/3.mp4',
    'assets_Kipra/videos/4.mp4',
    'assets_Kipra/videos/5.mp4',
    'assets_Kipra/videos/6.mp4',
    'assets_Kipra/videos/7.mp4',
    'assets_Kipra/videos/8.mp4',
    'assets_Kipra/videos/9.mp4',
    'assets_Kipra/videos/10.mp4',
  ];

  /// Get 11 mock users with first user as current logged-in user
  static List<UserModel_Kipra> getSampleUsers_Kipra() {
    return [
      // Current logged-in user (user_01)
      UserModel_Kipra(
        userId_Kipra: 'current_user_kipra_01',
        postIds_Kipra: [], // Current user has no posts in this mock
        avatarPath_Kipra: 'assets_Kipra/images/avatar_01.png',
        nickname_Kipra: 'VolleyChamp',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 60)),
        lastActiveAt_Kipra: DateTime.now(),
        userLevel_Kipra: 1,
        bio_Kipra: 'New to volleyball community, eager to learn!',
        location_Kipra: 'Tokyo, Japan',
        followingCount_Kipra: 5,
        followersCount_Kipra: 2,
      ),

      // Other users (user_02 to user_11)
      UserModel_Kipra(
        userId_Kipra: 'user_kipra_02',
        postIds_Kipra: ['post_kipra_01'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_02.png',
        nickname_Kipra: 'SpikeKing',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 45)),
        lastActiveAt_Kipra: DateTime.now().subtract(const Duration(hours: 3)),
        userLevel_Kipra: 8,
        bio_Kipra: 'Professional spiker with 10+ years experience',
        location_Kipra: 'Los Angeles, USA',
        followingCount_Kipra: 234,
        followersCount_Kipra: 1250,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_03',
        postIds_Kipra: ['post_kipra_02'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_03.png',
        nickname_Kipra: 'SetMaster',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 30)),
        lastActiveAt_Kipra: DateTime.now().subtract(const Duration(hours: 1)),
        userLevel_Kipra: 7,
        bio_Kipra: 'Setter specialist, team captain for 5 years',
        location_Kipra: 'São Paulo, Brazil',
        followingCount_Kipra: 189,
        followersCount_Kipra: 890,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_04',
        postIds_Kipra: ['post_kipra_03'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_04.png',
        nickname_Kipra: 'BlockWall',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 25)),
        lastActiveAt_Kipra:
            DateTime.now().subtract(const Duration(minutes: 45)),
        userLevel_Kipra: 6,
        bio_Kipra: 'Middle blocker, defensive specialist',
        location_Kipra: 'Berlin, Germany',
        followingCount_Kipra: 156,
        followersCount_Kipra: 670,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_05',
        postIds_Kipra: ['post_kipra_04'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_05.png',
        nickname_Kipra: 'DigMachine',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 40)),
        lastActiveAt_Kipra: DateTime.now().subtract(const Duration(hours: 2)),
        userLevel_Kipra: 5,
        bio_Kipra: 'Libero position, dig specialist and trainer',
        location_Kipra: 'Sydney, Australia',
        followingCount_Kipra: 98,
        followersCount_Kipra: 445,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_06',
        postIds_Kipra: ['post_kipra_05'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_06.png',
        nickname_Kipra: 'ServeAce',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 35)),
        lastActiveAt_Kipra: DateTime.now().subtract(const Duration(hours: 4)),
        userLevel_Kipra: 6,
        bio_Kipra: 'Serving specialist, tournament champion',
        location_Kipra: 'Paris, France',
        followingCount_Kipra: 167,
        followersCount_Kipra: 720,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_07',
        postIds_Kipra: ['post_kipra_06'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_07.png',
        nickname_Kipra: 'CoachPro',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 90)),
        lastActiveAt_Kipra: DateTime.now().subtract(const Duration(hours: 6)),
        userLevel_Kipra: 9,
        bio_Kipra: 'Professional volleyball coach, 15+ years experience',
        location_Kipra: 'Rome, Italy',
        followingCount_Kipra: 345,
        followersCount_Kipra: 2100,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_08',
        postIds_Kipra: ['post_kipra_07'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_08.png',
        nickname_Kipra: 'JumpHigh',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 20)),
        lastActiveAt_Kipra:
            DateTime.now().subtract(const Duration(minutes: 30)),
        userLevel_Kipra: 4,
        bio_Kipra: 'Outside hitter, vertical jump specialist',
        location_Kipra: 'Toronto, Canada',
        followingCount_Kipra: 87,
        followersCount_Kipra: 320,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_09',
        postIds_Kipra: ['post_kipra_08'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_09.png',
        nickname_Kipra: 'TeamCaptain',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 55)),
        lastActiveAt_Kipra: DateTime.now().subtract(const Duration(hours: 8)),
        userLevel_Kipra: 7,
        bio_Kipra: 'Team leader, strategic player and motivator',
        location_Kipra: 'Seoul, South Korea',
        followingCount_Kipra: 201,
        followersCount_Kipra: 950,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_10',
        postIds_Kipra: ['post_kipra_09'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_10.png',
        nickname_Kipra: 'PowerHitter',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 15)),
        lastActiveAt_Kipra: DateTime.now().subtract(const Duration(hours: 12)),
        userLevel_Kipra: 5,
        bio_Kipra: 'Opposite hitter, power attack specialist',
        location_Kipra: 'Mexico City, Mexico',
        followingCount_Kipra: 134,
        followersCount_Kipra: 580,
      ),

      UserModel_Kipra(
        userId_Kipra: 'user_kipra_11',
        postIds_Kipra: ['post_kipra_10'],
        avatarPath_Kipra: 'assets_Kipra/images/avatar_11.png',
        nickname_Kipra: 'VolleyNewbie',
        coinBalance_Kipra: 300,
        likedPostIds_Kipra: [],
        blockedUserIds_Kipra: [],
        reportedPostIds_Kipra: [],
        createdAt_Kipra: DateTime.now().subtract(const Duration(days: 5)),
        lastActiveAt_Kipra:
            DateTime.now().subtract(const Duration(minutes: 15)),
        userLevel_Kipra: 1,
        bio_Kipra: 'Just started playing volleyball, learning basics',
        location_Kipra: 'London, UK',
        followingCount_Kipra: 12,
        followersCount_Kipra: 8,
      ),
    ];
  }

  /// Get current logged-in user
  static UserModel_Kipra getCurrentUser_Kipra() {
    return getSampleUsers_Kipra().first;
  }

  /// Get random tags for posts (2-4 tags each)
  static List<String> _getRandomTags_Kipra() {
    final tagCount = 2 + _random.nextInt(3); // 2-4 tags
    final shuffledTags = List<String>.from(_volleyballTags_Kipra)
      ..shuffle(_random);
    return shuffledTags.take(tagCount).toList();
  }

  /// Get random users for comments (excluding current user)
  static List<UserModel_Kipra> _getRandomCommenters_Kipra() {
    final users = getSampleUsers_Kipra().skip(1).toList(); // Skip current user
    users.shuffle(_random);
    return users.take(3).toList(); // Take 3 random users
  }

  /// Get 10 mock posts with video paths and volleyball content
  static List<PostModel_Kipra> getSamplePosts_Kipra() {
    final users =
        getSampleUsers_Kipra().skip(1).toList(); // Exclude current user

    final postTitles = [
      'Master the Perfect Volleyball Serve',
      'Advanced Spiking Techniques for Power',
      'Defensive Blocking Strategies',
      'Setting Fundamentals Every Player Needs',
      'Digging Skills for Libero Position',
      'Team Coordination and Communication',
      'Tournament Preparation and Mental Game',
      'Intensive Training Session Highlights',
      'Professional Match Analysis',
      'Volleyball Basics for Beginners',
    ];

    final postContents = [
      'Learn the fundamentals of a powerful and accurate volleyball serve. This comprehensive tutorial covers stance, ball toss, contact point, and follow-through techniques.',
      'Elevate your attacking game with these advanced spiking techniques. Focus on approach timing, jump mechanics, and arm swing for maximum power and precision.',
      'Master the art of blocking with these defensive strategies. Learn proper hand positioning, timing, and team coordination for effective net defense.',
      'Essential setting techniques that every volleyball player should know. Covers hand positioning, footwork, and decision-making for consistent sets.',
      'Specialized digging techniques for defensive specialists. Learn proper body positioning, platform angle, and reaction time for successful digs.',
      'Improve your team play with effective communication and coordination strategies. Essential for competitive volleyball success.',
      'Mental preparation and physical training tips for tournament success. Includes warm-up routines and pressure management techniques.',
      'Intensive training session showcasing professional-level drills and conditioning exercises for serious volleyball players.',
      'Detailed analysis of professional volleyball matches. Learn from the best players and understand advanced game strategies.',
      'Perfect introduction to volleyball for new players. Covers basic rules, positions, and fundamental skills to get started.',
    ];

    return List.generate(10, (index) {
      final user = users[index];
      final comments =
          _generateCommentsForPost_Kipra('post_kipra_${index + 1}');

      return PostModel_Kipra(
        postId_Kipra: 'post_kipra_${index + 1}',
        authorId_Kipra: user.userId_Kipra,
        videoPath_Kipra: _videoPaths_Kipra[index],
        imagePaths_Kipra: [],
        likeCount_Kipra: 50 + _random.nextInt(500),
        commentCount_Kipra: comments.length,
        viewCount_Kipra: 200 + _random.nextInt(2000),
        title_Kipra: postTitles[index],
        content_Kipra: postContents[index],
        tags_Kipra: _getRandomTags_Kipra(),
        comments_Kipra: comments,
        createdAt_Kipra:
            DateTime.now().subtract(Duration(hours: 1 + _random.nextInt(72))),
        updatedAt_Kipra:
            DateTime.now().subtract(Duration(minutes: _random.nextInt(60))),
        postType_Kipra: 'video',
        isPinned_Kipra: index == 0, // Pin first post
        isDeleted_Kipra: false,
        status_Kipra: 'published',
        location_Kipra: index % 2 == 0 ? 'Training Center' : null,
      );
    });
  }

  /// Generate comments for a post
  static List<CommentModel_Kipra> _generateCommentsForPost_Kipra(
      String postId) {
    final commenters = _getRandomCommenters_Kipra();
    final commentContents = [
      'Great tutorial! This really helped improve my technique.',
      'Amazing content! Thanks for sharing these professional tips.',
      'This is exactly what I needed to work on. Very helpful!',
      'Excellent demonstration of proper form and technique.',
      'Love the detailed explanation. Keep up the great work!',
      'This video helped me understand the mechanics better.',
      'Perfect timing for this tutorial. Tournament coming up!',
      'Clear instructions and great visual examples.',
      'Been struggling with this technique. This helps a lot!',
      'Professional quality content. Really appreciate it!',
    ];

    return List.generate(3, (index) {
      final commenter = commenters[index];
      return CommentModel_Kipra(
        commentId_Kipra: '${postId}_comment_${index + 1}',
        commenterId_Kipra: commenter.userId_Kipra,
        content_Kipra: commentContents[_random.nextInt(commentContents.length)],
        createdAt_Kipra: DateTime.now()
            .subtract(Duration(minutes: 10 + _random.nextInt(120))),
        likeCount_Kipra: _random.nextInt(20),
        replyToCommentId_Kipra: null,
        replyToUserId_Kipra: null,
        isDeleted_Kipra: false,
      );
    });
  }
}
