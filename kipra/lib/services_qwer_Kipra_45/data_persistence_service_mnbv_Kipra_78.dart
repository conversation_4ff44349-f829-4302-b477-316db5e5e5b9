import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models_qwer_Kipra_84/models_export_Kipra.dart';
import '../data_mnop_Kipra_47/enhanced_sample_data_hjkl_Kipra_92.dart';

/// Data persistence service for Kipra volleyball community
/// Handles local storage of users, posts, and comments using SharedPreferences
class DataPersistenceService_Kipra {
  static const String _usersKey_Kipra = 'kipra_users_data';
  static const String _postsKey_Kipra = 'kipra_posts_data';
  static const String _currentUserKey_Kipra = 'kipra_current_user';
  static const String _isInitializedKey_Kipra = 'kipra_data_initialized';

  static SharedPreferences? _prefs;

  /// Initialize the service
  static Future<void> initialize_Kipra() async {
    _prefs ??= await SharedPreferences.getInstance();
    
    // Check if data is already initialized
    final isInitialized = _prefs!.getBool(_isInitializedKey_<PERSON><PERSON>) ?? false;
    
    if (!isInitialized) {
      await _initializeWithSampleData_Kipra();
      await _prefs!.setBool(_isInitializedKey_Kipra, true);
    }
  }

  /// Initialize with sample data
  static Future<void> _initializeWithSampleData_Kipra() async {
    final users = EnhancedSampleData_Kipra.getSampleUsers_Kipra();
    final posts = EnhancedSampleData_Kipra.getSamplePosts_Kipra();
    
    await saveUsers_Kipra(users);
    await savePosts_Kipra(posts);
    await setCurrentUser_Kipra(users.first); // Set first user as current
  }

  /// Save users to local storage
  static Future<bool> saveUsers_Kipra(List<UserModel_Kipra> users) async {
    try {
      await _ensureInitialized_Kipra();
      final usersJson = users.map((user) => user.toJson()).toList();
      final jsonString = jsonEncode(usersJson);
      return await _prefs!.setString(_usersKey_Kipra, jsonString);
    } catch (e) {
      print('Error saving users: $e');
      return false;
    }
  }

  /// Load users from local storage
  static Future<List<UserModel_Kipra>> loadUsers_Kipra() async {
    try {
      await _ensureInitialized_Kipra();
      final jsonString = _prefs!.getString(_usersKey_Kipra);
      
      if (jsonString == null) {
        // Return sample data if no saved data exists
        return EnhancedSampleData_Kipra.getSampleUsers_Kipra();
      }
      
      final List<dynamic> usersJson = jsonDecode(jsonString);
      return usersJson.map((json) => UserModel_Kipra.fromJson(json)).toList();
    } catch (e) {
      print('Error loading users: $e');
      return EnhancedSampleData_Kipra.getSampleUsers_Kipra();
    }
  }

  /// Save posts to local storage
  static Future<bool> savePosts_Kipra(List<PostModel_Kipra> posts) async {
    try {
      await _ensureInitialized_Kipra();
      final postsJson = posts.map((post) => post.toJson()).toList();
      final jsonString = jsonEncode(postsJson);
      return await _prefs!.setString(_postsKey_Kipra, jsonString);
    } catch (e) {
      print('Error saving posts: $e');
      return false;
    }
  }

  /// Load posts from local storage
  static Future<List<PostModel_Kipra>> loadPosts_Kipra() async {
    try {
      await _ensureInitialized_Kipra();
      final jsonString = _prefs!.getString(_postsKey_Kipra);
      
      if (jsonString == null) {
        // Return sample data if no saved data exists
        return EnhancedSampleData_Kipra.getSamplePosts_Kipra();
      }
      
      final List<dynamic> postsJson = jsonDecode(jsonString);
      return postsJson.map((json) => PostModel_Kipra.fromJson(json)).toList();
    } catch (e) {
      print('Error loading posts: $e');
      return EnhancedSampleData_Kipra.getSamplePosts_Kipra();
    }
  }

  /// Set current user
  static Future<bool> setCurrentUser_Kipra(UserModel_Kipra user) async {
    try {
      await _ensureInitialized_Kipra();
      final userJson = jsonEncode(user.toJson());
      return await _prefs!.setString(_currentUserKey_Kipra, userJson);
    } catch (e) {
      print('Error setting current user: $e');
      return false;
    }
  }

  /// Get current user
  static Future<UserModel_Kipra?> getCurrentUser_Kipra() async {
    try {
      await _ensureInitialized_Kipra();
      final jsonString = _prefs!.getString(_currentUserKey_Kipra);
      
      if (jsonString == null) {
        // Return first user from sample data if no current user is set
        final users = await loadUsers_Kipra();
        return users.isNotEmpty ? users.first : null;
      }
      
      final userJson = jsonDecode(jsonString);
      return UserModel_Kipra.fromJson(userJson);
    } catch (e) {
      print('Error getting current user: $e');
      final users = await loadUsers_Kipra();
      return users.isNotEmpty ? users.first : null;
    }
  }

  /// Update user data
  static Future<bool> updateUser_Kipra(UserModel_Kipra updatedUser) async {
    try {
      final users = await loadUsers_Kipra();
      final userIndex = users.indexWhere((user) => user.userId_Kipra == updatedUser.userId_Kipra);
      
      if (userIndex != -1) {
        users[userIndex] = updatedUser;
        await saveUsers_Kipra(users);
        
        // Update current user if it's the same user
        final currentUser = await getCurrentUser_Kipra();
        if (currentUser?.userId_Kipra == updatedUser.userId_Kipra) {
          await setCurrentUser_Kipra(updatedUser);
        }
        
        return true;
      }
      return false;
    } catch (e) {
      print('Error updating user: $e');
      return false;
    }
  }

  /// Update post data
  static Future<bool> updatePost_Kipra(PostModel_Kipra updatedPost) async {
    try {
      final posts = await loadPosts_Kipra();
      final postIndex = posts.indexWhere((post) => post.postId_Kipra == updatedPost.postId_Kipra);
      
      if (postIndex != -1) {
        posts[postIndex] = updatedPost;
        return await savePosts_Kipra(posts);
      }
      return false;
    } catch (e) {
      print('Error updating post: $e');
      return false;
    }
  }

  /// Add new post
  static Future<bool> addPost_Kipra(PostModel_Kipra newPost) async {
    try {
      final posts = await loadPosts_Kipra();
      posts.insert(0, newPost); // Add to beginning of list
      return await savePosts_Kipra(posts);
    } catch (e) {
      print('Error adding post: $e');
      return false;
    }
  }

  /// Get user by ID
  static Future<UserModel_Kipra?> getUserById_Kipra(String userId) async {
    try {
      final users = await loadUsers_Kipra();
      return users.firstWhere((user) => user.userId_Kipra == userId);
    } catch (e) {
      print('Error getting user by ID: $e');
      return null;
    }
  }

  /// Get post by ID
  static Future<PostModel_Kipra?> getPostById_Kipra(String postId) async {
    try {
      final posts = await loadPosts_Kipra();
      return posts.firstWhere((post) => post.postId_Kipra == postId);
    } catch (e) {
      print('Error getting post by ID: $e');
      return null;
    }
  }

  /// Get filtered posts for current user (excluding blocked users and reported posts)
  static Future<List<PostModel_Kipra>> getFilteredPosts_Kipra() async {
    try {
      final currentUser = await getCurrentUser_Kipra();
      if (currentUser == null) return [];
      
      final posts = await loadPosts_Kipra();
      
      return posts.where((post) {
        // Filter blocked users' posts
        if (currentUser.blockedUserIds_Kipra.contains(post.authorId_Kipra)) {
          return false;
        }
        
        // Filter reported posts
        if (currentUser.reportedPostIds_Kipra.contains(post.postId_Kipra)) {
          return false;
        }
        
        // Filter deleted posts
        if (post.isDeleted_Kipra) {
          return false;
        }
        
        return true;
      }).toList();
    } catch (e) {
      print('Error getting filtered posts: $e');
      return [];
    }
  }

  /// Clear all data (for testing purposes)
  static Future<bool> clearAllData_Kipra() async {
    try {
      await _ensureInitialized_Kipra();
      await _prefs!.remove(_usersKey_Kipra);
      await _prefs!.remove(_postsKey_Kipra);
      await _prefs!.remove(_currentUserKey_Kipra);
      await _prefs!.remove(_isInitializedKey_Kipra);
      return true;
    } catch (e) {
      print('Error clearing data: $e');
      return false;
    }
  }

  /// Ensure SharedPreferences is initialized
  static Future<void> _ensureInitialized_Kipra() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Reset to sample data
  static Future<bool> resetToSampleData_Kipra() async {
    try {
      await clearAllData_Kipra();
      await _initializeWithSampleData_Kipra();
      await _prefs!.setBool(_isInitializedKey_Kipra, true);
      return true;
    } catch (e) {
      print('Error resetting to sample data: $e');
      return false;
    }
  }
}
