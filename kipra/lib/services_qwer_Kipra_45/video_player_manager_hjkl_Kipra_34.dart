import 'dart:io';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

/// Video player manager for handling multiple video players
/// Ensures only one video plays at a time
class VideoPlayerManager_Kipra {
  static final VideoPlayerManager_Kipra _instance =
      VideoPlayerManager_Kipra._internal();
  factory VideoPlayerManager_Kipra() => _instance;
  VideoPlayerManager_Kipra._internal();

  final Map<String, VideoPlayerController> _controllers = {};
  String? _currentPlayingId;

  /// Get or create video controller for a post
  VideoPlayerController? getController(String postId, String videoPath) {
    if (!_controllers.containsKey(postId)) {
      try {
        VideoPlayerController controller;

        // Check the type of video path
        if (videoPath.startsWith('http')) {
          // Network URL
          controller = VideoPlayerController.networkUrl(Uri.parse(videoPath));
        } else if (videoPath.startsWith('/') ||
            videoPath.contains('tmp') ||
            videoPath.contains('Documents')) {
          // Absolute file path or temporary path (user recorded/selected video)
          controller = VideoPlayerController.file(File(videoPath));
        } else {
          // Asset path (bundled with app)
          controller = VideoPlayerController.asset(videoPath);
        }

        _controllers[postId] = controller;
        controller.initialize();
      } catch (e) {
        debugPrint('Error creating video controller for path $videoPath: $e');
        debugPrint(
            'Path type check: startsWith(/): ${videoPath.startsWith('/')}, contains(tmp): ${videoPath.contains('tmp')}, contains(Documents): ${videoPath.contains('Documents')}');
        return null;
      }
    }
    return _controllers[postId];
  }

  /// Play video and pause all others
  Future<void> playVideo(String postId) async {
    // Pause current playing video
    if (_currentPlayingId != null && _currentPlayingId != postId) {
      await pauseVideo(_currentPlayingId!);
    }

    final controller = _controllers[postId];
    if (controller != null && controller.value.isInitialized) {
      await controller.play();
      _currentPlayingId = postId;
    }
  }

  /// Pause specific video
  Future<void> pauseVideo(String postId) async {
    final controller = _controllers[postId];
    if (controller != null && controller.value.isInitialized) {
      await controller.pause();
      if (_currentPlayingId == postId) {
        _currentPlayingId = null;
      }
    }
  }

  /// Check if video is playing
  bool isPlaying(String postId) {
    final controller = _controllers[postId];
    return controller?.value.isPlaying ?? false;
  }

  /// Toggle play/pause for a video
  Future<void> togglePlayPause(String postId) async {
    if (isPlaying(postId)) {
      await pauseVideo(postId);
    } else {
      await playVideo(postId);
    }
  }

  /// Dispose specific controller
  void disposeController(String postId) {
    final controller = _controllers[postId];
    if (controller != null) {
      controller.dispose();
      _controllers.remove(postId);
      if (_currentPlayingId == postId) {
        _currentPlayingId = null;
      }
    }
  }

  /// Dispose all controllers
  void disposeAll() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
    _currentPlayingId = null;
  }

  /// Get current playing video ID
  String? get currentPlayingId => _currentPlayingId;
}
