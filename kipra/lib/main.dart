import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';
import 'views_efgh_Kipra_61/main_scaffold_page_tyui_Kipra_58.dart';

void main() {
  runApp(
    const ProviderScope(
      child: Ki<PERSON>App(),
    ),
  );
}

class KipraApp extends StatelessWidget {
  const KipraApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: 'Kipra - Volleyball First Pass Community',
          debugShowCheckedModeBanner: false,
          theme: AppTheme_Kipra.lightTheme,
          darkTheme: AppTheme_Kipra.darkTheme,
          home: const MainScaffoldPage_Kipra(),
        );
      },
    );
  }
}
