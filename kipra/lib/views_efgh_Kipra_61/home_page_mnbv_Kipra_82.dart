import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:video_player/video_player.dart';
import '../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';
import '../data_mnop_Kipra_47/sample_data_qwer_Kipra_83.dart';
import '../models_qwer_Kipra_84/models_export_Kipra.dart';
import '../services_qwer_Kipra_45/video_player_manager_hjkl_Kipra_34.dart';
import '../services_qwer_Kipra_45/data_persistence_service_mnbv_Kipra_78.dart';
import '../widgets_ijkl_Kipra_73/common_qrst_Kipra_81/comment_bottom_sheet_mnop_Kipra_67.dart';

class HomePage_Kipra extends StatefulWidget {
  const HomePage_Kipra({super.key});

  @override
  State<HomePage_Kipra> createState() => _HomePageState_Kipra();
}

// Static reference to home page state for refreshing
_HomePageState_Kipra? _homePageStateInstance;

// Global function to refresh home page data
void refreshHomePageData_Kipra() {
  _homePageStateInstance?.refreshData();
}

class _HomePageState_Kipra extends State<HomePage_Kipra> {
  List<PostModel_Kipra> _featuredPosts = [];
  List<UserModel_Kipra> _users = [];
  UserModel_Kipra? _currentUser;
  bool _isLoading = true;
  final VideoPlayerManager_Kipra _videoManager = VideoPlayerManager_Kipra();
  Timer? _uiUpdateTimer;

  @override
  void initState() {
    super.initState();
    _homePageStateInstance = this; // Set global reference
    _loadFeaturedContent();
    // Start a timer to periodically update UI for video playback states
    _uiUpdateTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _homePageStateInstance = null; // Clear global reference
    _uiUpdateTimer?.cancel();
    _videoManager.disposeAll();
    super.dispose();
  }

  // Public method to refresh data (called after publishing a post)
  void refreshData() {
    _loadFeaturedContent();
  }

  Future<void> _toggleLike(PostModel_Kipra post) async {
    if (_currentUser == null) return;

    final isLiked =
        _currentUser!.likedPostIds_Kipra.contains(post.postId_Kipra);
    final updatedUser = _currentUser!.copyWith(
      likedPostIds_Kipra: isLiked
          ? _currentUser!.likedPostIds_Kipra
              .where((id) => id != post.postId_Kipra)
              .toList()
          : [..._currentUser!.likedPostIds_Kipra, post.postId_Kipra],
    );

    final updatedPost = post.copyWith(
      likeCount_Kipra:
          isLiked ? post.likeCount_Kipra - 1 : post.likeCount_Kipra + 1,
    );

    // Update local state
    setState(() {
      _currentUser = updatedUser;
      final postIndex =
          _featuredPosts.indexWhere((p) => p.postId_Kipra == post.postId_Kipra);
      if (postIndex != -1) {
        _featuredPosts[postIndex] = updatedPost;
      }
    });

    // Save to persistence
    await DataPersistenceService_Kipra.setCurrentUser_Kipra(updatedUser);
    await DataPersistenceService_Kipra.updatePost_Kipra(updatedPost);
  }

  void _onPostUpdated(PostModel_Kipra updatedPost) {
    setState(() {
      final postIndex = _featuredPosts
          .indexWhere((p) => p.postId_Kipra == updatedPost.postId_Kipra);
      if (postIndex != -1) {
        _featuredPosts[postIndex] = updatedPost;
      }
    });
  }

  Future<void> _incrementViewCount(PostModel_Kipra post) async {
    final updatedPost = post.copyWith(
      viewCount_Kipra: post.viewCount_Kipra + 1,
    );

    // Update local state
    setState(() {
      final postIndex =
          _featuredPosts.indexWhere((p) => p.postId_Kipra == post.postId_Kipra);
      if (postIndex != -1) {
        _featuredPosts[postIndex] = updatedPost;
      }
    });

    // Save to persistence
    await DataPersistenceService_Kipra.updatePost_Kipra(updatedPost);
  }

  void _showCommentBottomSheet(PostModel_Kipra post) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CommentBottomSheet_Kipra(
        post: post,
        onPostUpdated: _onPostUpdated,
      ),
    ).then((_) {
      // Unfocus any input when bottom sheet closes
      FocusScope.of(context).unfocus();
    });
  }

  Future<void> _loadFeaturedContent() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize persistence service
      await DataPersistenceService_Kipra.initialize_Kipra();

      // Load data from persistence
      final posts = await DataPersistenceService_Kipra.loadPosts_Kipra();
      final users = await DataPersistenceService_Kipra.loadUsers_Kipra();
      final currentUser =
          await DataPersistenceService_Kipra.getCurrentUser_Kipra();

      setState(() {
        _featuredPosts =
            posts.take(6).toList(); // Take first 6 posts for featured content
        _users = users;
        _currentUser = currentUser ??
            users.first; // Use persisted current user or first user
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading featured content: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_Kipra.backgroundColor,
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: MediaQuery.of(context).padding.top + 20.h),
              _buildHeader(),
              SizedBox(height: 30.h),
              _buildWelcomeSection(),
              SizedBox(height: 30.h),
              _buildFeaturedContent(),
              SizedBox(height: 30.h),
              _buildRecentActivity(),
              SizedBox(height: 100.h), // Space for floating navigation
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Good Morning',
              style: GoogleFonts.poppins(
                fontSize: 14.sp,
                color: AppTheme_Kipra.textSecondary,
                fontWeight: FontWeight.w400,
              ),
            ),
            Text(
              'Volleyball Player',
              style: GoogleFonts.poppins(
                fontSize: 24.sp,
                color: AppTheme_Kipra.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Container(
          width: 48.w,
          height: 48.h,
          decoration: BoxDecoration(
            color: AppTheme_Kipra.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Icon(
            FontAwesomeIcons.volleyball,
            color: AppTheme_Kipra.primaryColor,
            size: 24.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme_Kipra.primaryColor,
            AppTheme_Kipra.primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.fire,
                color: Colors.white,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Ready to Spike?',
                style: GoogleFonts.poppins(
                  fontSize: 18.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            'Connect with players, share techniques,\nand elevate your volleyball game.',
            style: GoogleFonts.poppins(
              fontSize: 14.sp,
              color: Colors.white.withOpacity(0.9),
              fontWeight: FontWeight.w400,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: GoogleFonts.poppins(
            fontSize: 20.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                icon: FontAwesomeIcons.userPlus,
                title: 'Find Players',
                subtitle: 'Connect',
                color: AppTheme_Kipra.accentColor1,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildActionCard(
                icon: FontAwesomeIcons.dumbbell,
                title: 'Practice Drills',
                subtitle: 'Improve',
                color: AppTheme_Kipra.accentColor3,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.shadowColor,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20.sp,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 14.sp,
              color: AppTheme_Kipra.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            subtitle,
            style: GoogleFonts.poppins(
              fontSize: 12.sp,
              color: AppTheme_Kipra.textSecondary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Content',
              style: GoogleFonts.poppins(
                fontSize: 20.sp,
                color: AppTheme_Kipra.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        if (_isLoading)
          Container(
            height: 200.h,
            child: Center(
              child: CircularProgressIndicator(
                color: AppTheme_Kipra.primaryColor,
              ),
            ),
          )
        else
          Column(
            children: _featuredPosts.asMap().entries.map((entry) {
              final index = entry.key;
              final post = entry.value;
              final author = _users.firstWhere(
                (user) => user.userId_Kipra == post.authorId_Kipra,
                orElse: () => _users.first,
              );
              return Padding(
                padding: EdgeInsets.only(
                    bottom: index == _featuredPosts.length - 1 ? 0 : 16.h),
                child: _buildVideoCard(post, author),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildVideoCard(PostModel_Kipra post, UserModel_Kipra author) {
    // Calculate 9:16 aspect ratio height based on screen width
    final screenWidth = MediaQuery.of(context).size.width - 40.w;
    final cardHeight = screenWidth * (16 / 9);
    final controller =
        _videoManager.getController(post.postId_Kipra, post.videoPath_Kipra);
    final isPlaying = controller?.value.isPlaying ?? false;
    final isLiked =
        _currentUser?.likedPostIds_Kipra.contains(post.postId_Kipra) ?? false;

    return Column(
      children: [
        // Video card
        Container(
          width: double.infinity,
          height: cardHeight,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: AppTheme_Kipra.shadowColor,
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16.r),
            child: Stack(
              children: [
                // Video background
                GestureDetector(
                  onTap: () async {
                    final wasPlaying = controller?.value.isPlaying ?? false;
                    await _videoManager.togglePlayPause(post.postId_Kipra);

                    // If video started playing, increment view count
                    if (!wasPlaying && (controller?.value.isPlaying ?? false)) {
                      await _incrementViewCount(post);
                    }

                    setState(() {}); // Refresh to update play button visibility
                  },
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppTheme_Kipra.primaryColor.withOpacity(0.8),
                          AppTheme_Kipra.primaryColor.withOpacity(0.4),
                          Colors.black.withOpacity(0.6),
                        ],
                      ),
                    ),
                    child: _buildVideoPlayer(post),
                  ),
                ),
                // Play/Pause button
                if (!isPlaying)
                  Center(
                    child: GestureDetector(
                      onTap: () async {
                        final wasPlaying = controller?.value.isPlaying ?? false;
                        await _videoManager.togglePlayPause(post.postId_Kipra);

                        // If video started playing, increment view count
                        if (!wasPlaying &&
                            (controller?.value.isPlaying ?? false)) {
                          await _incrementViewCount(post);
                        }

                        setState(
                            () {}); // Refresh to update play button visibility
                      },
                      child: Container(
                        width: 60.w,
                        height: 60.h,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          FontAwesomeIcons.play,
                          color: AppTheme_Kipra.primaryColor,
                          size: 24.sp,
                        ),
                      ),
                    ),
                  ),
                // Author info overlay
                Positioned(
                  bottom: 0.h, // Leave space for data bar
                  left: 0.w,
                  right: 0.w,
                  child: Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Row(
                      children: [
                        // Author avatar
                        CircleAvatar(
                          radius: 20.r,
                          backgroundImage: AssetImage(author.avatarPath_Kipra),
                        ),
                        SizedBox(width: 12.w),
                        // Author info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                author.nickname_Kipra,
                                style: GoogleFonts.poppins(
                                  fontSize: 16.sp,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                post.title_Kipra,
                                style: GoogleFonts.poppins(
                                  fontSize: 12.sp,
                                  color: Colors.white.withOpacity(0.9),
                                  fontWeight: FontWeight.w400,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        // Data bar
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Row(
            children: [
              // Like button
              GestureDetector(
                onTap: () => _toggleLike(post),
                child: Row(
                  children: [
                    Icon(
                      isLiked
                          ? FontAwesomeIcons.solidHeart
                          : FontAwesomeIcons.heart,
                      color:
                          isLiked ? Colors.red : AppTheme_Kipra.textSecondary,
                      size: 20.sp,
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      '${post.likeCount_Kipra}',
                      style: GoogleFonts.poppins(
                        fontSize: 14.sp,
                        color: AppTheme_Kipra.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 24.w),
              // Comment button
              GestureDetector(
                onTap: () => _showCommentBottomSheet(post),
                child: Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.comment,
                      color: AppTheme_Kipra.textSecondary,
                      size: 20.sp,
                    ),
                    SizedBox(width: 6.w),
                    Text(
                      '${post.commentCount_Kipra}',
                      style: GoogleFonts.poppins(
                        fontSize: 14.sp,
                        color: AppTheme_Kipra.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 24.w),
              // View count
              Row(
                children: [
                  Icon(
                    FontAwesomeIcons.eye,
                    color: AppTheme_Kipra.textSecondary,
                    size: 20.sp,
                  ),
                  SizedBox(width: 6.w),
                  Text(
                    '${post.viewCount_Kipra}',
                    style: GoogleFonts.poppins(
                      fontSize: 14.sp,
                      color: AppTheme_Kipra.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              // More button
              GestureDetector(
                onTap: () {
                  // TODO: Show more options
                },
                child: Icon(
                  FontAwesomeIcons.ellipsisVertical,
                  color: AppTheme_Kipra.textSecondary,
                  size: 20.sp,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVideoPlayer(PostModel_Kipra post) {
    final controller =
        _videoManager.getController(post.postId_Kipra, post.videoPath_Kipra);

    if (controller == null || !controller.value.isInitialized) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme_Kipra.primaryColor.withOpacity(0.8),
              AppTheme_Kipra.accentColor1.withOpacity(0.6),
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2.w,
              ),
              SizedBox(height: 12.h),
              Text(
                'Loading Video...',
                style: GoogleFonts.poppins(
                  fontSize: 14.sp,
                  color: Colors.white.withOpacity(0.8),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: FittedBox(
        fit: BoxFit.cover,
        child: SizedBox(
          width: controller.value.size.width,
          height: controller.value.size.height,
          child: VideoPlayer(controller),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: GoogleFonts.poppins(
            fontSize: 20.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        _buildActivityItem(
          icon: FontAwesomeIcons.userCheck,
          title: 'New teammate joined',
          subtitle: '2 minutes ago',
          color: AppTheme_Kipra.accentColor3,
        ),
        SizedBox(height: 12.h),
        _buildActivityItem(
          icon: FontAwesomeIcons.trophy,
          title: 'Drill completed',
          subtitle: '1 hour ago',
          color: AppTheme_Kipra.accentColor1,
        ),
        SizedBox(height: 12.h),
        _buildActivityItem(
          icon: FontAwesomeIcons.heart,
          title: 'Video liked',
          subtitle: '3 hours ago',
          color: AppTheme_Kipra.primaryColor,
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppTheme_Kipra.borderColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 36.w,
            height: 36.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 14.sp,
                    color: AppTheme_Kipra.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.poppins(
                    fontSize: 12.sp,
                    color: AppTheme_Kipra.textSecondary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
