import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';

class PublishPage_Kipra extends StatefulWidget {
  const PublishPage_Kipra({super.key});

  @override
  State<PublishPage_Kipra> createState() => _PublishPageState_Kipra();
}

class _PublishPageState_Kipra extends State<PublishPage_Kipra> {
  int _selectedContentType_Kipra = 0;
  final TextEditingController _titleController_Kipra = TextEditingController();
  final TextEditingController _descriptionController_Kipra =
      TextEditingController();

  @override
  void dispose() {
    _titleController_Kipra.dispose();
    _descriptionController_Ki<PERSON>.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_Kipra.backgroundColor,
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 20.h),
                _buildHeader(),
                SizedBox(height: 30.h),
                _buildContentTypeSelector(),
                SizedBox(height: 30.h),
                _buildMediaUploadSection(),
                SizedBox(height: 30.h),
                _buildContentForm(),
                SizedBox(height: 30.h),
                _buildTagsSection(),
                SizedBox(height: 30.h),
                _buildPublishButton(),
                SizedBox(height: 100.h), // Space for floating navigation
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          width: 48.w,
          height: 48.h,
          decoration: BoxDecoration(
            color: AppTheme_Kipra.accentColor1.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Icon(
            FontAwesomeIcons.plus,
            color: AppTheme_Kipra.accentColor1,
            size: 24.sp,
          ),
        ),
        SizedBox(width: 16.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Create Content',
              style: GoogleFonts.poppins(
                fontSize: 24.sp,
                color: AppTheme_Kipra.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'Share your volleyball journey',
              style: GoogleFonts.poppins(
                fontSize: 14.sp,
                color: AppTheme_Kipra.textSecondary,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContentTypeSelector() {
    final contentTypes_Kipra = [
      {
        'icon': FontAwesomeIcons.video,
        'title': 'Video',
        'subtitle': 'Share techniques'
      },
      {
        'icon': FontAwesomeIcons.image,
        'title': 'Photo',
        'subtitle': 'Capture moments'
      },
      {
        'icon': FontAwesomeIcons.dumbbell,
        'title': 'Drill',
        'subtitle': 'Training routine'
      },
      {
        'icon': FontAwesomeIcons.lightbulb,
        'title': 'Tip',
        'subtitle': 'Share knowledge'
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Content Type',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12.w,
            mainAxisSpacing: 12.h,
            childAspectRatio: 1.2,
          ),
          itemCount: contentTypes_Kipra.length,
          itemBuilder: (context, index) {
            final isSelected = _selectedContentType_Kipra == index;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedContentType_Kipra = index;
                });
              },
              child: Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppTheme_Kipra.primaryColor.withOpacity(0.1)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme_Kipra.primaryColor
                        : AppTheme_Kipra.borderColor,
                    width: isSelected ? 2 : 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme_Kipra.shadowColor,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      contentTypes_Kipra[index]['icon'] as IconData,
                      color: isSelected
                          ? AppTheme_Kipra.primaryColor
                          : AppTheme_Kipra.textSecondary,
                      size: 24.sp,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      contentTypes_Kipra[index]['title'] as String,
                      style: GoogleFonts.poppins(
                        fontSize: 14.sp,
                        color: isSelected
                            ? AppTheme_Kipra.primaryColor
                            : AppTheme_Kipra.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      contentTypes_Kipra[index]['subtitle'] as String,
                      style: GoogleFonts.poppins(
                        fontSize: 12.sp,
                        color: AppTheme_Kipra.textSecondary,
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildMediaUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Media Upload',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          width: double.infinity,
          height: 200.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
              color: AppTheme_Kipra.borderColor,
              width: 2,
              style: BorderStyle.solid,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: AppTheme_Kipra.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Icon(
                  FontAwesomeIcons.cloudArrowUp,
                  color: AppTheme_Kipra.primaryColor,
                  size: 28.sp,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'Tap to upload media',
                style: GoogleFonts.poppins(
                  fontSize: 16.sp,
                  color: AppTheme_Kipra.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Support video, photo formats',
                style: GoogleFonts.poppins(
                  fontSize: 14.sp,
                  color: AppTheme_Kipra.textSecondary,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Content Details',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        TextField(
          controller: _titleController_Kipra,
          decoration: InputDecoration(
            hintText: 'Enter title...',
            hintStyle: GoogleFonts.poppins(
              fontSize: 14.sp,
              color: AppTheme_Kipra.textTertiary,
            ),
            prefixIcon: Icon(
              FontAwesomeIcons.heading,
              color: AppTheme_Kipra.textSecondary,
              size: 18.sp,
            ),
          ),
          style: GoogleFonts.poppins(
            fontSize: 14.sp,
            color: AppTheme_Kipra.textPrimary,
          ),
        ),
        SizedBox(height: 16.h),
        TextField(
          controller: _descriptionController_Kipra,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'Write description...',
            hintStyle: GoogleFonts.poppins(
              fontSize: 14.sp,
              color: AppTheme_Kipra.textTertiary,
            ),
            prefixIcon: Padding(
              padding: EdgeInsets.only(top: 12.h),
              child: Icon(
                FontAwesomeIcons.alignLeft,
                color: AppTheme_Kipra.textSecondary,
                size: 18.sp,
              ),
            ),
          ),
          style: GoogleFonts.poppins(
            fontSize: 14.sp,
            color: AppTheme_Kipra.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildTagsSection() {
    final popularTags_Kipra = [
      '#volleyball',
      '#training',
      '#technique',
      '#serve',
      '#spike',
      '#defense'
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Popular Tags',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: popularTags_Kipra.map((tag) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppTheme_Kipra.accentColor2.withOpacity(0.3),
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: AppTheme_Kipra.accentColor2,
                  width: 1,
                ),
              ),
              child: Text(
                tag,
                style: GoogleFonts.poppins(
                  fontSize: 12.sp,
                  color: AppTheme_Kipra.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPublishButton() {
    return Container(
      width: double.infinity,
      height: 56.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            AppTheme_Kipra.primaryColor,
            AppTheme_Kipra.primaryColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.primaryColor.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          // Handle publish action
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.paperPlane,
              color: Colors.white,
              size: 18.sp,
            ),
            SizedBox(width: 12.w),
            Text(
              'Publish Content',
              style: GoogleFonts.poppins(
                fontSize: 16.sp,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
