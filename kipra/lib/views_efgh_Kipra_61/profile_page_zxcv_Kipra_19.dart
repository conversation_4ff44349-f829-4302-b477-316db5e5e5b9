import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';
import '../models_qwer_Kipra_84/models_export_Kipra.dart';
import '../services_qwer_Kipra_45/data_persistence_service_mnbv_Kipra_78.dart';

class ProfilePage_Kipra extends StatefulWidget {
  const ProfilePage_Kipra({super.key});

  @override
  State<ProfilePage_Kipra> createState() => _ProfilePageState_Kipra();
}

class _ProfilePageState_Kipra extends State<ProfilePage_Kipra> {
  UserModel_Kipra? _currentUser;
  List<PostModel_Kipra> _userPosts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      await DataPersistenceService_Kipra.initialize_Kipra();
      final currentUser =
          await DataPersistenceService_Kipra.getCurrentUser_Kipra();
      final allPosts = await DataPersistenceService_Kipra.loadPosts_Kipra();

      // Filter posts by current user
      final userPosts = allPosts
          .where((post) => post.authorId_Kipra == currentUser?.userId_Kipra)
          .toList();

      setState(() {
        _currentUser = currentUser;
        _userPosts = userPosts;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading user data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_Kipra.backgroundColor,
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 20.h),
                _buildHeader(),
                SizedBox(height: 30.h),
                _buildProfileCard(),
                SizedBox(height: 30.h),
                _buildStatsSection(),
                SizedBox(height: 30.h),
                _buildMenuSection(),
                SizedBox(height: 30.h),
                _buildSettingsSection(),
                SizedBox(height: 100.h), // Space for floating navigation
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Profile',
          style: GoogleFonts.poppins(
            fontSize: 24.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        Container(
          width: 48.w,
          height: 48.h,
          decoration: BoxDecoration(
            color: AppTheme_Kipra.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Icon(
            FontAwesomeIcons.gear,
            color: AppTheme_Kipra.primaryColor,
            size: 20.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildProfileCard() {
    if (_isLoading) {
      return Container(
        width: double.infinity,
        height: 200.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: AppTheme_Kipra.shadowColor,
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Center(
          child: CircularProgressIndicator(
            color: AppTheme_Kipra.primaryColor,
          ),
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.shadowColor,
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // User avatar
          Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: _currentUser?.avatarPath_Kipra != null
                  ? DecorationImage(
                      image: AssetImage(_currentUser!.avatarPath_Kipra),
                      fit: BoxFit.cover,
                    )
                  : null,
              color: _currentUser?.avatarPath_Kipra == null
                  ? AppTheme_Kipra.primaryColor
                  : null,
            ),
            child: _currentUser?.avatarPath_Kipra == null
                ? Center(
                    child: Text(
                      _currentUser?.nickname_Kipra
                              .substring(0, 1)
                              .toUpperCase() ??
                          'U',
                      style: GoogleFonts.poppins(
                        fontSize: 32.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                : null,
          ),
          SizedBox(height: 16.h),
          Text(
            _currentUser?.nickname_Kipra ?? 'Unknown User',
            style: GoogleFonts.poppins(
              fontSize: 20.sp,
              color: AppTheme_Kipra.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'Volleyball Enthusiast',
            style: GoogleFonts.poppins(
              fontSize: 14.sp,
              color: AppTheme_Kipra.textSecondary,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FontAwesomeIcons.locationDot,
                color: AppTheme_Kipra.textSecondary,
                size: 14.sp,
              ),
              SizedBox(width: 6.w),
              Text(
                'San Francisco, CA',
                style: GoogleFonts.poppins(
                  fontSize: 12.sp,
                  color: AppTheme_Kipra.textSecondary,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          Container(
            width: double.infinity,
            height: 48.h,
            decoration: BoxDecoration(
              color: AppTheme_Kipra.primaryColor,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Edit Profile',
                style: GoogleFonts.poppins(
                  fontSize: 14.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.shadowColor,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'My Stats',
            style: GoogleFonts.poppins(
              fontSize: 18.sp,
              color: AppTheme_Kipra.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 20.h),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: FontAwesomeIcons.video,
                  value: '${_userPosts.length}',
                  label: 'Posts',
                  color: AppTheme_Kipra.accentColor1,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: FontAwesomeIcons.coins,
                  value: '${_currentUser?.coinBalance_Kipra ?? 0}',
                  label: 'Coins',
                  color: AppTheme_Kipra.primaryColor,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  icon: FontAwesomeIcons.heart,
                  value: '${_currentUser?.likedPostIds_Kipra.length ?? 0}',
                  label: 'Liked',
                  color: AppTheme_Kipra.accentColor3,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          width: 48.w,
          height: 48.h,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20.sp,
          ),
        ),
        SizedBox(height: 12.h),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w700,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12.sp,
            color: AppTheme_Kipra.textSecondary,
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuSection() {
    final menuItems_Kipra = [
      {
        'icon': FontAwesomeIcons.heart,
        'title': 'My Favorites',
        'subtitle': 'Saved content',
        'color': AppTheme_Kipra.accentColor1,
      },
      {
        'icon': FontAwesomeIcons.clock,
        'title': 'Training History',
        'subtitle': 'View progress',
        'color': AppTheme_Kipra.primaryColor,
      },
      {
        'icon': FontAwesomeIcons.medal,
        'title': 'Achievements',
        'subtitle': 'Your milestones',
        'color': AppTheme_Kipra.accentColor3,
      },
      {
        'icon': FontAwesomeIcons.userGroup,
        'title': 'My Teams',
        'subtitle': 'Team connections',
        'color': AppTheme_Kipra.primaryColor,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'My Activity',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        ...menuItems_Kipra
            .map((item) => _buildMenuItem(
                  icon: item['icon'] as IconData,
                  title: item['title'] as String,
                  subtitle: item['subtitle'] as String,
                  color: item['color'] as Color,
                ))
            .toList(),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppTheme_Kipra.borderColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              icon,
              color: color,
              size: 18.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 14.sp,
                    color: AppTheme_Kipra.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.poppins(
                    fontSize: 12.sp,
                    color: AppTheme_Kipra.textSecondary,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            FontAwesomeIcons.chevronRight,
            color: AppTheme_Kipra.textTertiary,
            size: 14.sp,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    final settingsItems_Kipra = [
      {
        'icon': FontAwesomeIcons.bell,
        'title': 'Notifications',
        'color': AppTheme_Kipra.accentColor1,
      },
      {
        'icon': FontAwesomeIcons.shield,
        'title': 'Privacy & Security',
        'color': AppTheme_Kipra.primaryColor,
      },
      {
        'icon': FontAwesomeIcons.circleQuestion,
        'title': 'Help & Support',
        'color': AppTheme_Kipra.accentColor3,
      },
      {
        'icon': FontAwesomeIcons.rightFromBracket,
        'title': 'Sign Out',
        'color': AppTheme_Kipra.errorColor,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Settings',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        ...settingsItems_Kipra
            .map((item) => _buildSettingsItem(
                  icon: item['icon'] as IconData,
                  title: item['title'] as String,
                  color: item['color'] as Color,
                ))
            .toList(),
      ],
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required Color color,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppTheme_Kipra.borderColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 18.sp,
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 14.sp,
                color: AppTheme_Kipra.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Icon(
            FontAwesomeIcons.chevronRight,
            color: AppTheme_Kipra.textTertiary,
            size: 14.sp,
          ),
        ],
      ),
    );
  }
}
