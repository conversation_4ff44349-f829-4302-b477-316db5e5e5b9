import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';

class StorePage_Kipra extends StatefulWidget {
  const StorePage_Kipra({super.key});

  @override
  State<StorePage_Kipra> createState() => _StorePageState_Kipra();
}

class _StorePageState_Kipra extends State<StorePage_Kipra> {
  int _selectedCategory_Kipra = 0;
  final TextEditingController _searchController_Kipra = TextEditingController();

  @override
  void dispose() {
    _searchController_Kipra.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_Kipra.backgroundColor,
        body: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 20.h),
                _buildHeader(),
                SizedBox(height: 20.h),
                _buildSearchBar(),
                SizedBox(height: 30.h),
                _buildCategorySelector(),
                SizedBox(height: 30.h),
                _buildFeaturedBanner(),
                SizedBox(height: 30.h),
                _buildProductGrid(),
                SizedBox(height: 100.h), // Space for floating navigation
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Volleyball Store',
              style: GoogleFonts.poppins(
                fontSize: 24.sp,
                color: AppTheme_Kipra.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'Premium gear for champions',
              style: GoogleFonts.poppins(
                fontSize: 14.sp,
                color: AppTheme_Kipra.textSecondary,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        Container(
          width: 48.w,
          height: 48.h,
          decoration: BoxDecoration(
            color: AppTheme_Kipra.accentColor1.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Stack(
            children: [
              Center(
                child: Icon(
                  FontAwesomeIcons.bagShopping,
                  color: AppTheme_Kipra.accentColor1,
                  size: 20.sp,
                ),
              ),
              Positioned(
                top: 8.h,
                right: 8.w,
                child: Container(
                  width: 16.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                    color: AppTheme_Kipra.accentColor1,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Center(
                    child: Text(
                      '3',
                      style: GoogleFonts.poppins(
                        fontSize: 10.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController_Kipra,
        decoration: InputDecoration(
          hintText: 'Search volleyball gear...',
          hintStyle: GoogleFonts.poppins(
            fontSize: 14.sp,
            color: AppTheme_Kipra.textTertiary,
          ),
          prefixIcon: Icon(
            FontAwesomeIcons.magnifyingGlass,
            color: AppTheme_Kipra.textSecondary,
            size: 18.sp,
          ),
          suffixIcon: Icon(
            FontAwesomeIcons.sliders,
            color: AppTheme_Kipra.textSecondary,
            size: 18.sp,
          ),
          border: InputBorder.none,
          contentPadding:
              EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        ),
        style: GoogleFonts.poppins(
          fontSize: 14.sp,
          color: AppTheme_Kipra.textPrimary,
        ),
      ),
    );
  }

  Widget _buildCategorySelector() {
    final categories_Kipra = [
      {'icon': FontAwesomeIcons.volleyball, 'title': 'Balls'},
      {'icon': FontAwesomeIcons.tshirt, 'title': 'Apparel'},
      {'icon': FontAwesomeIcons.shoePrints, 'title': 'Shoes'},
      {'icon': FontAwesomeIcons.dumbbell, 'title': 'Training'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 16.h),
        Row(
          children: categories_Kipra.asMap().entries.map((entry) {
            final index = entry.key;
            final category = entry.value;
            final isSelected = _selectedCategory_Kipra == index;

            return Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedCategory_Kipra = index;
                  });
                },
                child: Container(
                  margin: EdgeInsets.only(
                      right: index < categories_Kipra.length - 1 ? 12.w : 0),
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  decoration: BoxDecoration(
                    color:
                        isSelected ? AppTheme_Kipra.primaryColor : Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme_Kipra.shadowColor,
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        category['icon'] as IconData,
                        color: isSelected
                            ? Colors.white
                            : AppTheme_Kipra.textSecondary,
                        size: 20.sp,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        category['title'] as String,
                        style: GoogleFonts.poppins(
                          fontSize: 12.sp,
                          color: isSelected
                              ? Colors.white
                              : AppTheme_Kipra.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildFeaturedBanner() {
    return Container(
      width: double.infinity,
      height: 160.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme_Kipra.accentColor1,
            AppTheme_Kipra.accentColor1.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.accentColor1.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            left: 24.w,
            top: 24.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Summer Sale',
                  style: GoogleFonts.poppins(
                    fontSize: 20.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Up to 40% OFF',
                  style: GoogleFonts.poppins(
                    fontSize: 16.sp,
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 16.h),
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    'Shop Now',
                    style: GoogleFonts.poppins(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            right: 20.w,
            top: 20.h,
            child: Icon(
              FontAwesomeIcons.fire,
              color: Colors.white.withOpacity(0.3),
              size: 80.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductGrid() {
    final products_Kipra = [
      {
        'name': 'Pro Volleyball',
        'price': '\$29.99',
        'rating': '4.8',
        'image': FontAwesomeIcons.volleyball,
        'color': AppTheme_Kipra.primaryColor,
      },
      {
        'name': 'Training Shoes',
        'price': '\$89.99',
        'rating': '4.9',
        'image': FontAwesomeIcons.shoePrints,
        'color': AppTheme_Kipra.accentColor1,
      },
      {
        'name': 'Sports Jersey',
        'price': '\$39.99',
        'rating': '4.7',
        'image': FontAwesomeIcons.tshirt,
        'color': AppTheme_Kipra.accentColor3,
      },
      {
        'name': 'Training Kit',
        'price': '\$59.99',
        'rating': '4.6',
        'image': FontAwesomeIcons.dumbbell,
        'color': AppTheme_Kipra.primaryColor,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Products',
              style: GoogleFonts.poppins(
                fontSize: 18.sp,
                color: AppTheme_Kipra.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'View All',
              style: GoogleFonts.poppins(
                fontSize: 14.sp,
                color: AppTheme_Kipra.primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16.w,
            mainAxisSpacing: 16.h,
            childAspectRatio: 0.85,
          ),
          itemCount: products_Kipra.length,
          itemBuilder: (context, index) {
            final product = products_Kipra[index];
            return _buildProductCard(product);
          },
        ),
      ],
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.shadowColor,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: (product['color'] as Color).withOpacity(0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Center(
                child: Icon(
                  product['image'] as IconData,
                  color: product['color'] as Color,
                  size: 40.sp,
                ),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Padding(
              padding: EdgeInsets.all(10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    product['name'] as String,
                    style: GoogleFonts.poppins(
                      fontSize: 13.sp,
                      color: AppTheme_Kipra.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 2.h),
                  Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.solidStar,
                        color: Colors.amber,
                        size: 10.sp,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        product['rating'] as String,
                        style: GoogleFonts.poppins(
                          fontSize: 11.sp,
                          color: AppTheme_Kipra.textSecondary,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Text(
                    product['price'] as String,
                    style: GoogleFonts.poppins(
                      fontSize: 14.sp,
                      color: AppTheme_Kipra.primaryColor,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
