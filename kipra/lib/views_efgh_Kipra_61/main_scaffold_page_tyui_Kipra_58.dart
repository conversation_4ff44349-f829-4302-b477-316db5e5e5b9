import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../widgets_ijkl_Kipra_73/common_qrst_Kipra_81/floating_navigation_bar_mnop_Kipra_46.dart';
import '../widgets_ijkl_Kipra_73/publish_qwer_Kipra_45/publish_bottom_sheet_mnbv_Kipra_78.dart';
import 'home_page_mnbv_Kipra_82.dart';
import 'publish_page_qwer_Kipra_45.dart';
import 'store_page_asdf_Kipra_67.dart';
import 'profile_page_zxcv_Kipra_19.dart';
import '../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';

class MainScaffoldPage_Kipra extends StatefulWidget {
  const MainScaffoldPage_Kipra({super.key});

  @override
  State<MainScaffoldPage_Kipra> createState() => _MainScaffoldPageState_Kipra();
}

class _MainScaffoldPageState_Kipra extends State<MainScaffoldPage_Kipra>
    with TickerProviderStateMixin {
  int _currentIndex_Kipra = 0;
  late PageController _pageController_Kipra;
  late AnimationController _animationController_Kipra;
  late Animation<double> _fadeAnimation_Kipra;

  final List<Widget> _pages_Kipra = [
    const HomePage_Kipra(),
    const ProfilePage_Kipra(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController_Kipra = PageController(initialPage: _currentIndex_Kipra);
    _animationController_Kipra = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation_Kipra = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController_Kipra,
      curve: Curves.easeInOut,
    ));
    _animationController_Kipra.forward();
  }

  @override
  void dispose() {
    _pageController_Kipra.dispose();
    _animationController_Kipra.dispose();
    super.dispose();
  }

  // Convert page index back to navigation index for display
  int _getNavigationIndex() {
    // Pages: 0=Home, 1=Profile
    // Navigation: 0=Home, 1=Publish, 2=Profile
    switch (_currentIndex_Kipra) {
      case 0:
        return 0; // Home
      case 1:
        return 2; // Profile
      default:
        return 0;
    }
  }

  void _onNavigationTapped_Kipra(int index) {
    // Handle publish button (index 1) differently
    if (index == 1) {
      _showPublishBottomSheet();
      return;
    }

    // Map navigation index to page index
    // Navigation: 0=Home, 1=Publish, 2=Profile
    // Pages: 0=Home, 1=Profile
    int pageIndex;
    if (index == 0) {
      pageIndex = 0; // Home
    } else if (index == 2) {
      pageIndex = 1; // Profile
    } else {
      return; // Invalid index
    }

    if (_currentIndex_Kipra != pageIndex) {
      setState(() {
        _currentIndex_Kipra = pageIndex;
      });

      // Animate to the selected page
      _pageController_Kipra.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOutCubic,
      );

      // Add a subtle animation effect
      _animationController_Kipra.reset();
      _animationController_Kipra.forward();
    }
  }

  void _showPublishBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PublishBottomSheet_Kipra(
        onPostPublished: () {
          // Refresh home page data after publishing
          refreshHomePageData_Kipra();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_Kipra.backgroundColor,
        extendBody: true, // Allow body to extend behind the floating navigation
        body: Stack(
          children: [
            FadeTransition(
              opacity: _fadeAnimation_Kipra,
              child: PageView(
                controller: _pageController_Kipra,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex_Kipra = index;
                  });
                },
                physics: const BouncingScrollPhysics(),
                children: _pages_Kipra,
              ),
            ),
            FloatingNavigationBar_Kipra(
              currentIndex_Kipra: _getNavigationIndex(),
              onTap_Kipra: _onNavigationTapped_Kipra,
            ),
          ],
        ),
      ),
    );
  }
}

// Alternative version with modern navigation bar
class ModernMainScaffoldPage_Kipra extends StatefulWidget {
  const ModernMainScaffoldPage_Kipra({super.key});

  @override
  State<ModernMainScaffoldPage_Kipra> createState() =>
      _ModernMainScaffoldPageState_Kipra();
}

class _ModernMainScaffoldPageState_Kipra
    extends State<ModernMainScaffoldPage_Kipra> with TickerProviderStateMixin {
  int _currentIndex_Kipra = 0;
  late PageController _pageController_Kipra;
  late AnimationController _slideAnimationController_Kipra;
  late Animation<Offset> _slideAnimation_Kipra;

  final List<Widget> _pages_Kipra = [
    const HomePage_Kipra(),
    const PublishPage_Kipra(),
    const StorePage_Kipra(),
    const ProfilePage_Kipra(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController_Kipra = PageController(initialPage: _currentIndex_Kipra);
    _slideAnimationController_Kipra = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideAnimation_Kipra = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController_Kipra,
      curve: Curves.easeOutBack,
    ));

    // Delay the navigation bar animation
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _slideAnimationController_Kipra.forward();
      }
    });
  }

  @override
  void dispose() {
    _pageController_Kipra.dispose();
    _slideAnimationController_Kipra.dispose();
    super.dispose();
  }

  void _onNavigationTapped_Kipra(int index) {
    if (_currentIndex_Kipra != index) {
      setState(() {
        _currentIndex_Kipra = index;
      });

      _pageController_Kipra.animateToPage(
        index,
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_Kipra.backgroundColor,
        extendBody: true,
        body: Stack(
          children: [
            // Main content with subtle parallax effect
            PageView.builder(
              controller: _pageController_Kipra,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex_Kipra = index;
                });
              },
              physics: const BouncingScrollPhysics(),
              itemCount: _pages_Kipra.length,
              itemBuilder: (context, index) {
                return AnimatedBuilder(
                  animation: _pageController_Kipra,
                  builder: (context, child) {
                    double value = 1.0;
                    if (_pageController_Kipra.position.haveDimensions) {
                      value = _pageController_Kipra.page! - index;
                      value = (1 - (value.abs() * 0.1)).clamp(0.0, 1.0);
                    }
                    return Transform.scale(
                      scale: value,
                      child: Opacity(
                        opacity: value,
                        child: _pages_Kipra[index],
                      ),
                    );
                  },
                );
              },
            ),

            // Animated Modern Navigation Bar
            SlideTransition(
              position: _slideAnimation_Kipra,
              child: ModernFloatingNavigationBar_Kipra(
                currentIndex_Kipra: _currentIndex_Kipra,
                onTap_Kipra: _onNavigationTapped_Kipra,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Glassmorphism version
class GlassmorphismMainScaffoldPage_Kipra extends StatefulWidget {
  const GlassmorphismMainScaffoldPage_Kipra({super.key});

  @override
  State<GlassmorphismMainScaffoldPage_Kipra> createState() =>
      _GlassmorphismMainScaffoldPageState_Kipra();
}

class _GlassmorphismMainScaffoldPageState_Kipra
    extends State<GlassmorphismMainScaffoldPage_Kipra>
    with TickerProviderStateMixin {
  int _currentIndex_Kipra = 0;
  late PageController _pageController_Kipra;
  late AnimationController _scaleAnimationController_Kipra;
  late Animation<double> _scaleAnimation_Kipra;

  final List<Widget> _pages_Kipra = [
    const HomePage_Kipra(),
    const PublishPage_Kipra(),
    const StorePage_Kipra(),
    const ProfilePage_Kipra(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController_Kipra = PageController(initialPage: _currentIndex_Kipra);
    _scaleAnimationController_Kipra = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleAnimation_Kipra = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleAnimationController_Kipra,
      curve: Curves.elasticOut,
    ));

    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _scaleAnimationController_Kipra.forward();
      }
    });
  }

  @override
  void dispose() {
    _pageController_Kipra.dispose();
    _scaleAnimationController_Kipra.dispose();
    super.dispose();
  }

  void _onNavigationTapped_Kipra(int index) {
    if (_currentIndex_Kipra != index) {
      setState(() {
        _currentIndex_Kipra = index;
      });

      _pageController_Kipra.animateToPage(
        index,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOutQuart,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: AppTheme_Kipra.backgroundColor,
        extendBody: true,
        body: Stack(
          children: [
            // Background gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme_Kipra.backgroundColor,
                    AppTheme_Kipra.backgroundColor.withOpacity(0.95),
                  ],
                ),
              ),
            ),

            // Main content
            PageView(
              controller: _pageController_Kipra,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex_Kipra = index;
                });
              },
              physics: const BouncingScrollPhysics(),
              children: _pages_Kipra,
            ),

            // Glassmorphism Navigation Bar
            ScaleTransition(
              scale: _scaleAnimation_Kipra,
              child: GlassmorphismNavigationBar_Kipra(
                currentIndex_Kipra: _currentIndex_Kipra,
                onTap_Kipra: _onNavigationTapped_Kipra,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
