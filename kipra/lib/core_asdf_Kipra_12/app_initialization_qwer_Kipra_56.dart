import '../services_qwer_Kipra_45/data_persistence_service_mnbv_Kipra_78.dart';
import '../data_mnop_Kipra_47/sample_data_qwer_Kipra_83.dart';

/// App initialization service for Kipra volleyball community
/// Handles initialization of data persistence and other core services
class AppInitialization_Kipra {
  static bool _isInitialized = false;

  /// Initialize the app with all required services
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize data persistence service
      await DataPersistenceService_Kipra.initialize_Kipra();
      
      // Initialize sample data service
      await SampleData_Kipra.initializeData_Kipra();
      
      _isInitialized = true;
      print('Kipra app initialization completed successfully');
    } catch (e) {
      print('Error during app initialization: $e');
      rethrow;
    }
  }

  /// Check if app is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset initialization state (for testing)
  static void resetInitialization() {
    _isInitialized = false;
  }
}
