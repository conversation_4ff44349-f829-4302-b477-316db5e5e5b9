// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_model_zxcv_Kipra_56.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostModel_Kipra _$PostModel_KipraFromJson(Map<String, dynamic> json) =>
    PostModel_Kipra(
      postId_Kipra: json['post_id'] as String,
      authorId_Kipra: json['author_id'] as String,
      videoPath_Kipra: json['video_path'] as String? ?? '',
      imagePaths_Kipra: (json['image_paths'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      likeCount_Kipra: (json['like_count'] as num?)?.toInt() ?? 0,
      commentCount_Kipra: (json['comment_count'] as num?)?.toInt() ?? 0,
      viewCount_Kipra: (json['view_count'] as num?)?.toInt() ?? 0,
      title_Kipra: json['title'] as String,
      content_Kipra: json['content'] as String,
      tags_Kipra:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              [],
      comments_Kipra: (json['comments'] as List<dynamic>?)
              ?.map(
                  (e) => CommentModel_Kipra.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt_Kipra: DateTime.parse(json['created_at'] as String),
      updatedAt_Kipra: DateTime.parse(json['updated_at'] as String),
      postType_Kipra: json['post_type'] as String? ?? 'text',
      isPinned_Kipra: json['is_pinned'] as bool? ?? false,
      isDeleted_Kipra: json['is_deleted'] as bool? ?? false,
      status_Kipra: json['status'] as String? ?? 'published',
      location_Kipra: json['location'] as String?,
    );

Map<String, dynamic> _$PostModel_KipraToJson(PostModel_Kipra instance) =>
    <String, dynamic>{
      'post_id': instance.postId_Kipra,
      'author_id': instance.authorId_Kipra,
      'video_path': instance.videoPath_Kipra,
      'image_paths': instance.imagePaths_Kipra,
      'like_count': instance.likeCount_Kipra,
      'comment_count': instance.commentCount_Kipra,
      'view_count': instance.viewCount_Kipra,
      'title': instance.title_Kipra,
      'content': instance.content_Kipra,
      'tags': instance.tags_Kipra,
      'comments': instance.comments_Kipra,
      'created_at': instance.createdAt_Kipra.toIso8601String(),
      'updated_at': instance.updatedAt_Kipra.toIso8601String(),
      'post_type': instance.postType_Kipra,
      'is_pinned': instance.isPinned_Kipra,
      'is_deleted': instance.isDeleted_Kipra,
      'status': instance.status_Kipra,
      'location': instance.location_Kipra,
    };
