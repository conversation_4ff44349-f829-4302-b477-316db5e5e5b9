import 'package:json_annotation/json_annotation.dart';
import 'comment_model_asdf_Kipra_91.dart';

part 'post_model_zxcv_Kipra_56.g.dart';

/// 帖子数据模型
/// 包含帖子的所有信息和评论数据
@JsonSerializable()
class PostModel_Kipra {
  /// 帖子唯一标识ID
  @JsonKey(name: 'post_id')
  final String postId_Kipra;

  /// 作者用户ID（与用户模型的userId映射）
  @JsonKey(name: 'author_id')
  final String authorId_Kipra;

  /// 帖子的视频路径
  @JsonKey(name: 'video_path', defaultValue: '')
  final String videoPath_Kipra;

  /// 帖子的图片路径集合（支持多图）
  @JsonKey(name: 'image_paths', defaultValue: <String>[])
  final List<String> imagePaths_Kipra;

  /// 点赞数
  @JsonKey(name: 'like_count', defaultValue: 0)
  final int likeCount_Kipra;

  /// 评论数
  @J<PERSON><PERSON><PERSON>(name: 'comment_count', defaultValue: 0)
  final int commentCount_Kipra;

  /// 浏览数
  @Json<PERSON>ey(name: 'view_count', defaultValue: 0)
  final int viewCount_Kipra;

  /// 帖子标题
  @JsonKey(name: 'title')
  final String title_Kipra;

  /// 帖子详情内容
  @JsonKey(name: 'content')
  final String content_Kipra;

  /// 帖子标签集合
  @JsonKey(name: 'tags', defaultValue: <String>[])
  final List<String> tags_Kipra;

  /// 评论区数据
  @JsonKey(name: 'comments', defaultValue: <CommentModel_Kipra>[])
  final List<CommentModel_Kipra> comments_Kipra;

  /// 帖子创建时间
  @JsonKey(name: 'created_at')
  final DateTime createdAt_Kipra;

  /// 帖子最后更新时间
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt_Kipra;

  /// 帖子类型（视频、图片、文字等）
  @JsonKey(name: 'post_type', defaultValue: 'text')
  final String postType_Kipra;

  /// 是否置顶
  @JsonKey(name: 'is_pinned', defaultValue: false)
  final bool isPinned_Kipra;

  /// 是否被删除
  @JsonKey(name: 'is_deleted', defaultValue: false)
  final bool isDeleted_Kipra;

  /// 帖子状态（草稿、已发布、审核中等）
  @JsonKey(name: 'status', defaultValue: 'published')
  final String status_Kipra;

  /// 地理位置信息
  @JsonKey(name: 'location')
  final String? location_Kipra;

  /// 构造函数
  const PostModel_Kipra({
    required this.postId_Kipra,
    required this.authorId_Kipra,
    required this.videoPath_Kipra,
    required this.imagePaths_Kipra,
    required this.likeCount_Kipra,
    required this.commentCount_Kipra,
    required this.viewCount_Kipra,
    required this.title_Kipra,
    required this.content_Kipra,
    required this.tags_Kipra,
    required this.comments_Kipra,
    required this.createdAt_Kipra,
    required this.updatedAt_Kipra,
    required this.postType_Kipra,
    required this.isPinned_Kipra,
    required this.isDeleted_Kipra,
    required this.status_Kipra,
    this.location_Kipra,
  });

  /// 从JSON创建帖子模型
  factory PostModel_Kipra.fromJson(Map<String, dynamic> json) =>
      _$PostModel_KipraFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$PostModel_KipraToJson(this);

  /// 创建帖子模型副本
  PostModel_Kipra copyWith({
    String? postId_Kipra,
    String? authorId_Kipra,
    String? videoPath_Kipra,
    List<String>? imagePaths_Kipra,
    int? likeCount_Kipra,
    int? commentCount_Kipra,
    int? viewCount_Kipra,
    String? title_Kipra,
    String? content_Kipra,
    List<String>? tags_Kipra,
    List<CommentModel_Kipra>? comments_Kipra,
    DateTime? createdAt_Kipra,
    DateTime? updatedAt_Kipra,
    String? postType_Kipra,
    bool? isPinned_Kipra,
    bool? isDeleted_Kipra,
    String? status_Kipra,
    String? location_Kipra,
  }) {
    return PostModel_Kipra(
      postId_Kipra: postId_Kipra ?? this.postId_Kipra,
      authorId_Kipra: authorId_Kipra ?? this.authorId_Kipra,
      videoPath_Kipra: videoPath_Kipra ?? this.videoPath_Kipra,
      imagePaths_Kipra: imagePaths_Kipra ?? this.imagePaths_Kipra,
      likeCount_Kipra: likeCount_Kipra ?? this.likeCount_Kipra,
      commentCount_Kipra: commentCount_Kipra ?? this.commentCount_Kipra,
      viewCount_Kipra: viewCount_Kipra ?? this.viewCount_Kipra,
      title_Kipra: title_Kipra ?? this.title_Kipra,
      content_Kipra: content_Kipra ?? this.content_Kipra,
      tags_Kipra: tags_Kipra ?? this.tags_Kipra,
      comments_Kipra: comments_Kipra ?? this.comments_Kipra,
      createdAt_Kipra: createdAt_Kipra ?? this.createdAt_Kipra,
      updatedAt_Kipra: updatedAt_Kipra ?? this.updatedAt_Kipra,
      postType_Kipra: postType_Kipra ?? this.postType_Kipra,
      isPinned_Kipra: isPinned_Kipra ?? this.isPinned_Kipra,
      isDeleted_Kipra: isDeleted_Kipra ?? this.isDeleted_Kipra,
      status_Kipra: status_Kipra ?? this.status_Kipra,
      location_Kipra: location_Kipra ?? this.location_Kipra,
    );
  }

  /// 是否有视频内容
  bool get hasVideo_Kipra => videoPath_Kipra.isNotEmpty;

  /// 是否有图片内容
  bool get hasImages_Kipra => imagePaths_Kipra.isNotEmpty;

  /// 是否有媒体内容（视频或图片）
  bool get hasMedia_Kipra => hasVideo_Kipra || hasImages_Kipra;

  /// 增加点赞数
  PostModel_Kipra incrementLike_Kipra() {
    return copyWith(
      likeCount_Kipra: likeCount_Kipra + 1,
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 减少点赞数
  PostModel_Kipra decrementLike_Kipra() {
    return copyWith(
      likeCount_Kipra: (likeCount_Kipra - 1).clamp(0, double.infinity).toInt(),
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 增加浏览数
  PostModel_Kipra incrementView_Kipra() {
    return copyWith(
      viewCount_Kipra: viewCount_Kipra + 1,
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 添加评论
  PostModel_Kipra addComment_Kipra(CommentModel_Kipra comment) {
    final updatedComments = List<CommentModel_Kipra>.from(comments_Kipra)..add(comment);
    return copyWith(
      comments_Kipra: updatedComments,
      commentCount_Kipra: commentCount_Kipra + 1,
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 删除评论
  PostModel_Kipra removeComment_Kipra(String commentId) {
    final updatedComments = comments_Kipra.where((c) => c.commentId_Kipra != commentId).toList();
    return copyWith(
      comments_Kipra: updatedComments,
      commentCount_Kipra: (commentCount_Kipra - 1).clamp(0, double.infinity).toInt(),
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 更新评论
  PostModel_Kipra updateComment_Kipra(CommentModel_Kipra updatedComment) {
    final updatedComments = comments_Kipra.map((comment) {
      return comment.commentId_Kipra == updatedComment.commentId_Kipra 
          ? updatedComment 
          : comment;
    }).toList();
    
    return copyWith(
      comments_Kipra: updatedComments,
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 添加标签
  PostModel_Kipra addTag_Kipra(String tag) {
    if (!tags_Kipra.contains(tag)) {
      final updatedTags = List<String>.from(tags_Kipra)..add(tag);
      return copyWith(
        tags_Kipra: updatedTags,
        updatedAt_Kipra: DateTime.now(),
      );
    }
    return this;
  }

  /// 移除标签
  PostModel_Kipra removeTag_Kipra(String tag) {
    final updatedTags = List<String>.from(tags_Kipra)..remove(tag);
    return copyWith(
      tags_Kipra: updatedTags,
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 标记为已删除
  PostModel_Kipra markAsDeleted_Kipra() {
    return copyWith(
      isDeleted_Kipra: true,
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 置顶/取消置顶
  PostModel_Kipra togglePin_Kipra() {
    return copyWith(
      isPinned_Kipra: !isPinned_Kipra,
      updatedAt_Kipra: DateTime.now(),
    );
  }

  /// 获取格式化的发布时间
  String getFormattedTime_Kipra() {
    final now = DateTime.now();
    final difference = now.difference(createdAt_Kipra);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${createdAt_Kipra.month}/${createdAt_Kipra.day}/${createdAt_Kipra.year}';
    }
  }

  /// 获取热度分数（基于点赞、评论、浏览数）
  double getHotScore_Kipra() {
    final timeFactor = DateTime.now().difference(createdAt_Kipra).inHours;
    final decayFactor = 1.0 / (1.0 + timeFactor * 0.1);
    
    return (likeCount_Kipra * 3 + commentCount_Kipra * 2 + viewCount_Kipra * 0.1) * decayFactor;
  }

  @override
  String toString() {
    return 'PostModel_Kipra{postId: $postId_Kipra, authorId: $authorId_Kipra, title: $title_Kipra}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PostModel_Kipra && other.postId_Kipra == postId_Kipra;
  }

  @override
  int get hashCode => postId_Kipra.hashCode;
}
