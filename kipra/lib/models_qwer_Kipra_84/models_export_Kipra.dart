/// Kipra应用数据模型统一导出文件
///
/// 包含用户模型、帖子模型、评论模型等核心数据结构
/// 使用json_annotation进行JSON序列化支持
///
/// 使用方式：
/// ```dart
/// import 'package:kipra/models_qwer_Kipra_84/models_export_Kipra.dart';
/// ```

// 用户相关模型
export 'user_model_tyui_Kipra_73.dart';

// 帖子相关模型
export 'post_model_zxcv_Kipra_56.dart';

// 评论相关模型
export 'comment_model_asdf_Kipra_91.dart';

/// 数据模型工具类
/// 提供一些通用的数据处理方法
class ModelUtils_Kipra {
  /// 生成唯一ID
  static String generateId_Kipra() {
    final timestamp = DateTime.now().microsecondsSinceEpoch;
    final random =
        (timestamp.hashCode % 10000).abs().toString().padLeft(4, '0');
    return '${timestamp}_$random';
  }

  /// 验证用户ID格式
  static bool isValidUserId_Kipra(String userId) {
    return userId.isNotEmpty && userId.length >= 3;
  }

  /// 验证帖子ID格式
  static bool isValidPostId_Kipra(String postId) {
    return postId.isNotEmpty && postId.length >= 3;
  }

  /// 验证评论ID格式
  static bool isValidCommentId_Kipra(String commentId) {
    return commentId.isNotEmpty && commentId.length >= 3;
  }

  /// 清理HTML标签（用于内容显示）
  static String cleanHtmlTags_Kipra(String content) {
    return content.replaceAll(RegExp(r'<[^>]*>'), '');
  }

  /// 截取内容预览
  static String getContentPreview_Kipra(String content, {int maxLength = 100}) {
    final cleanContent = cleanHtmlTags_Kipra(content);
    if (cleanContent.length <= maxLength) {
      return cleanContent;
    }
    return '${cleanContent.substring(0, maxLength)}...';
  }

  /// 格式化数字显示（如：1.2K, 1.5M）
  static String formatNumber_Kipra(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    }
  }

  /// 验证标签格式
  static bool isValidTag_Kipra(String tag) {
    return tag.isNotEmpty &&
        tag.length <= 20 &&
        !tag.contains(' ') &&
        RegExp(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$').hasMatch(tag);
  }

  /// 清理和格式化标签
  static String formatTag_Kipra(String tag) {
    return tag.trim().toLowerCase().replaceAll(' ', '_');
  }

  /// 检查内容是否包含敏感词（简单实现）
  static bool containsSensitiveWords_Kipra(String content) {
    final sensitiveWords = ['spam', 'fake', 'scam']; // 可扩展敏感词列表
    final lowerContent = content.toLowerCase();
    return sensitiveWords.any((word) => lowerContent.contains(word));
  }

  /// 获取文件扩展名
  static String getFileExtension_Kipra(String filePath) {
    return filePath.split('.').last.toLowerCase();
  }

  /// 检查是否为视频文件
  static bool isVideoFile_Kipra(String filePath) {
    final videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    return videoExtensions.contains(getFileExtension_Kipra(filePath));
  }

  /// 检查是否为图片文件
  static bool isImageFile_Kipra(String filePath) {
    final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.contains(getFileExtension_Kipra(filePath));
  }

  /// 计算两个时间的差值描述
  static String getTimeDifference_Kipra(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()}w ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else {
      return '${(difference.inDays / 365).floor()}y ago';
    }
  }

  /// 验证昵称格式
  static bool isValidNickname_Kipra(String nickname) {
    return nickname.isNotEmpty &&
        nickname.length >= 2 &&
        nickname.length <= 20 &&
        RegExp(r'^[a-zA-Z0-9_\u4e00-\u9fa5]+$').hasMatch(nickname);
  }

  /// 验证邮箱格式
  static bool isValidEmail_Kipra(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// 生成随机头像颜色
  static int generateAvatarColor_Kipra(String userId) {
    final hash = userId.hashCode;
    final colors = [
      0xFF194B7B, // Court Blue
      0xFFFF6E5C, // Spike Coral
      0xFFFFE8C9, // Serve Cream
      0xFFB0E4AD, // Volley Grass
      0xFF6366F1, // Indigo
      0xFF8B5CF6, // Purple
      0xFFEC4899, // Pink
      0xFF10B981, // Emerald
    ];
    return colors[hash.abs() % colors.length];
  }
}
