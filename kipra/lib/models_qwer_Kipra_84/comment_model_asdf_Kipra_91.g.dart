// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comment_model_asdf_Kipra_91.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommentModel_Kipra _$CommentModel_KipraFromJson(Map<String, dynamic> json) =>
    CommentModel_Kipra(
      commentId_Kipra: json['comment_id'] as String,
      commenterId_Kipra: json['commenter_id'] as String,
      content_Kipra: json['content'] as String,
      createdAt_Kipra: DateTime.parse(json['created_at'] as String),
      likeCount_Kipra: (json['like_count'] as num?)?.toInt() ?? 0,
      replyToCommentId_Kipra: json['reply_to_comment_id'] as String?,
      replyToUserId_Kipra: json['reply_to_user_id'] as String?,
      isDeleted_Kipra: json['is_deleted'] as bool? ?? false,
    );

Map<String, dynamic> _$CommentModel_KipraToJson(CommentModel_Kipra instance) =>
    <String, dynamic>{
      'comment_id': instance.commentId_Kipra,
      'commenter_id': instance.commenterId_Kipra,
      'content': instance.content_Kipra,
      'created_at': instance.createdAt_Kipra.toIso8601String(),
      'like_count': instance.likeCount_Kipra,
      'reply_to_comment_id': instance.replyToCommentId_Kipra,
      'reply_to_user_id': instance.replyToUserId_Kipra,
      'is_deleted': instance.isDeleted_Kipra,
    };
