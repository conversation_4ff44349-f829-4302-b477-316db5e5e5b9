// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model_tyui_Kipra_73.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel_Kipra _$UserModel_KipraFromJson(Map<String, dynamic> json) =>
    UserModel_Kipra(
      userId_Kipra: json['id'] as String,
      postIds_Kipra: (json['post_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      avatarPath_Kipra: json['avatar_path'] as String? ?? '',
      nickname_Kipra: json['nickname'] as String? ?? '',
      coinBalance_Kipra: (json['coin_balance'] as num?)?.toInt() ?? 0,
      likedPostIds_Kipra: (json['liked_post_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      blockedUserIds_Kipra: (json['blocked_user_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      reportedPostIds_Kipra: (json['reported_post_ids'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      createdAt_Kipra: DateTime.parse(json['created_at'] as String),
      lastActiveAt_Kipra: DateTime.parse(json['last_active_at'] as String),
      userLevel_Kipra: (json['user_level'] as num?)?.toInt() ?? 1,
      bio_Kipra: json['bio'] as String? ?? '',
      location_Kipra: json['location'] as String? ?? '',
      followingCount_Kipra: (json['following_count'] as num?)?.toInt() ?? 0,
      followersCount_Kipra: (json['followers_count'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$UserModel_KipraToJson(UserModel_Kipra instance) =>
    <String, dynamic>{
      'id': instance.userId_Kipra,
      'post_ids': instance.postIds_Kipra,
      'avatar_path': instance.avatarPath_Kipra,
      'nickname': instance.nickname_Kipra,
      'coin_balance': instance.coinBalance_Kipra,
      'liked_post_ids': instance.likedPostIds_Kipra,
      'blocked_user_ids': instance.blockedUserIds_Kipra,
      'reported_post_ids': instance.reportedPostIds_Kipra,
      'created_at': instance.createdAt_Kipra.toIso8601String(),
      'last_active_at': instance.lastActiveAt_Kipra.toIso8601String(),
      'user_level': instance.userLevel_Kipra,
      'bio': instance.bio_Kipra,
      'location': instance.location_Kipra,
      'following_count': instance.followingCount_Kipra,
      'followers_count': instance.followersCount_Kipra,
    };
