import 'package:json_annotation/json_annotation.dart';

part 'comment_model_asdf_Kipra_91.g.dart';

/// 评论数据模型
/// 用于帖子评论区的数据结构
@JsonSerializable()
class CommentModel_Kipra {
  /// 评论唯一标识ID
  @Json<PERSON>ey(name: 'comment_id')
  final String commentId_Kipra;

  /// 评论者用户ID（用来获取昵称头像）
  @JsonKey(name: 'commenter_id')
  final String commenterId_Kipra;

  /// 评论内容
  @Json<PERSON><PERSON>(name: 'content')
  final String content_Kipra;

  /// 评论时间
  @Json<PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt_Kipra;

  /// 评论点赞数
  @J<PERSON><PERSON><PERSON>(name: 'like_count', defaultValue: 0)
  final int likeCount_Kipra;

  /// 回复的评论ID（如果是回复评论）
  @JsonKey(name: 'reply_to_comment_id')
  final String? replyToCommentId_Kipra;

  /// 回复的用户ID（如果是回复某人）
  @Json<PERSON>ey(name: 'reply_to_user_id')
  final String? replyToUserId_Kipra;

  /// 是否被删除
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_deleted', defaultValue: false)
  final bool isDeleted_Kipra;

  /// 构造函数
  const CommentModel_Kipra({
    required this.commentId_Kipra,
    required this.commenterId_Kipra,
    required this.content_Kipra,
    required this.createdAt_Kipra,
    required this.likeCount_Kipra,
    this.replyToCommentId_Kipra,
    this.replyToUserId_Kipra,
    required this.isDeleted_Kipra,
  });

  /// 从JSON创建评论模型
  factory CommentModel_Kipra.fromJson(Map<String, dynamic> json) =>
      _$CommentModel_KipraFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$CommentModel_KipraToJson(this);

  /// 创建评论模型副本
  CommentModel_Kipra copyWith({
    String? commentId_Kipra,
    String? commenterId_Kipra,
    String? content_Kipra,
    DateTime? createdAt_Kipra,
    int? likeCount_Kipra,
    String? replyToCommentId_Kipra,
    String? replyToUserId_Kipra,
    bool? isDeleted_Kipra,
  }) {
    return CommentModel_Kipra(
      commentId_Kipra: commentId_Kipra ?? this.commentId_Kipra,
      commenterId_Kipra: commenterId_Kipra ?? this.commenterId_Kipra,
      content_Kipra: content_Kipra ?? this.content_Kipra,
      createdAt_Kipra: createdAt_Kipra ?? this.createdAt_Kipra,
      likeCount_Kipra: likeCount_Kipra ?? this.likeCount_Kipra,
      replyToCommentId_Kipra: replyToCommentId_Kipra ?? this.replyToCommentId_Kipra,
      replyToUserId_Kipra: replyToUserId_Kipra ?? this.replyToUserId_Kipra,
      isDeleted_Kipra: isDeleted_Kipra ?? this.isDeleted_Kipra,
    );
  }

  /// 是否是回复评论
  bool get isReply_Kipra => replyToCommentId_Kipra != null;

  /// 增加点赞数
  CommentModel_Kipra incrementLike_Kipra() {
    return copyWith(likeCount_Kipra: likeCount_Kipra + 1);
  }

  /// 减少点赞数
  CommentModel_Kipra decrementLike_Kipra() {
    return copyWith(likeCount_Kipra: (likeCount_Kipra - 1).clamp(0, double.infinity).toInt());
  }

  /// 标记为已删除
  CommentModel_Kipra markAsDeleted_Kipra() {
    return copyWith(isDeleted_Kipra: true);
  }

  /// 获取格式化的时间显示
  String getFormattedTime_Kipra() {
    final now = DateTime.now();
    final difference = now.difference(createdAt_Kipra);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${createdAt_Kipra.month}/${createdAt_Kipra.day}/${createdAt_Kipra.year}';
    }
  }

  @override
  String toString() {
    return 'CommentModel_Kipra{commentId: $commentId_Kipra, commenterId: $commenterId_Kipra, content: $content_Kipra}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommentModel_Kipra && other.commentId_Kipra == commentId_Kipra;
  }

  @override
  int get hashCode => commentId_Kipra.hashCode;
}
