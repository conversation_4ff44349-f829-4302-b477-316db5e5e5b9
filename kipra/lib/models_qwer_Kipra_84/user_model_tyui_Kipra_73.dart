import 'package:json_annotation/json_annotation.dart';

part 'user_model_tyui_Ki<PERSON>_73.g.dart';

/// 用户数据模型
/// 包含用户基本信息、帖子关联、社交功能等
@JsonSerializable()
class UserModel_Kipra {
  /// 用户唯一标识ID
  @JsonKey(name: 'id')
  final String userId_Kipra;

  /// 用户发布的帖子ID集合
  @JsonKey(name: 'post_ids', defaultValue: <String>[])
  final List<String> postIds_Kipra;

  /// 用户头像路径
  @JsonKey(name: 'avatar_path', defaultValue: '')
  final String avatarPath_Kipra;

  /// 用户昵称
  @JsonKey(name: 'nickname', defaultValue: '')
  final String nickname_Kipra;

  /// 金币余额
  @JsonKey(name: 'coin_balance', defaultValue: 0)
  final int coinBalance_Kipra;

  /// 用户喜欢的帖子ID集合
  /// 用于喜欢列表展示和喜欢/取消喜欢功能实现
  @Json<PERSON><PERSON>(name: 'liked_post_ids', defaultValue: <String>[])
  final List<String> likedPostIds_Kipra;

  /// 拉黑的用户ID集合
  /// 首页帖子展示时会过滤这些用户的所有帖子
  @JsonKey(name: 'blocked_user_ids', defaultValue: <String>[])
  final List<String> blockedUserIds_Kipra;

  /// 举报的帖子ID集合
  /// 首页帖子展示时会过滤这些被举报的帖子
  @JsonKey(name: 'reported_post_ids', defaultValue: <String>[])
  final List<String> reportedPostIds_Kipra;

  /// 用户注册时间
  @JsonKey(name: 'created_at')
  final DateTime createdAt_Kipra;

  /// 用户最后活跃时间
  @JsonKey(name: 'last_active_at')
  final DateTime lastActiveAt_Kipra;

  /// 用户等级（基于活跃度和贡献度）
  @JsonKey(name: 'user_level', defaultValue: 1)
  final int userLevel_Kipra;

  /// 用户简介
  @JsonKey(name: 'bio', defaultValue: '')
  final String bio_Kipra;

  /// 用户所在地区
  @JsonKey(name: 'location', defaultValue: '')
  final String location_Kipra;

  /// 用户关注的人数
  @JsonKey(name: 'following_count', defaultValue: 0)
  final int followingCount_Kipra;

  /// 用户粉丝数
  @JsonKey(name: 'followers_count', defaultValue: 0)
  final int followersCount_Kipra;

  /// 构造函数
  const UserModel_Kipra({
    required this.userId_Kipra,
    required this.postIds_Kipra,
    required this.avatarPath_Kipra,
    required this.nickname_Kipra,
    required this.coinBalance_Kipra,
    required this.likedPostIds_Kipra,
    required this.blockedUserIds_Kipra,
    required this.reportedPostIds_Kipra,
    required this.createdAt_Kipra,
    required this.lastActiveAt_Kipra,
    required this.userLevel_Kipra,
    required this.bio_Kipra,
    required this.location_Kipra,
    required this.followingCount_Kipra,
    required this.followersCount_Kipra,
  });

  /// 从JSON创建用户模型
  factory UserModel_Kipra.fromJson(Map<String, dynamic> json) =>
      _$UserModel_KipraFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$UserModel_KipraToJson(this);

  /// 创建用户模型副本（用于状态更新）
  UserModel_Kipra copyWith({
    String? userId_Kipra,
    List<String>? postIds_Kipra,
    String? avatarPath_Kipra,
    String? nickname_Kipra,
    int? coinBalance_Kipra,
    List<String>? likedPostIds_Kipra,
    List<String>? blockedUserIds_Kipra,
    List<String>? reportedPostIds_Kipra,
    DateTime? createdAt_Kipra,
    DateTime? lastActiveAt_Kipra,
    int? userLevel_Kipra,
    String? bio_Kipra,
    String? location_Kipra,
    int? followingCount_Kipra,
    int? followersCount_Kipra,
  }) {
    return UserModel_Kipra(
      userId_Kipra: userId_Kipra ?? this.userId_Kipra,
      postIds_Kipra: postIds_Kipra ?? this.postIds_Kipra,
      avatarPath_Kipra: avatarPath_Kipra ?? this.avatarPath_Kipra,
      nickname_Kipra: nickname_Kipra ?? this.nickname_Kipra,
      coinBalance_Kipra: coinBalance_Kipra ?? this.coinBalance_Kipra,
      likedPostIds_Kipra: likedPostIds_Kipra ?? this.likedPostIds_Kipra,
      blockedUserIds_Kipra: blockedUserIds_Kipra ?? this.blockedUserIds_Kipra,
      reportedPostIds_Kipra: reportedPostIds_Kipra ?? this.reportedPostIds_Kipra,
      createdAt_Kipra: createdAt_Kipra ?? this.createdAt_Kipra,
      lastActiveAt_Kipra: lastActiveAt_Kipra ?? this.lastActiveAt_Kipra,
      userLevel_Kipra: userLevel_Kipra ?? this.userLevel_Kipra,
      bio_Kipra: bio_Kipra ?? this.bio_Kipra,
      location_Kipra: location_Kipra ?? this.location_Kipra,
      followingCount_Kipra: followingCount_Kipra ?? this.followingCount_Kipra,
      followersCount_Kipra: followersCount_Kipra ?? this.followersCount_Kipra,
    );
  }

  /// 检查是否喜欢某个帖子
  bool isPostLiked_Kipra(String postId) {
    return likedPostIds_Kipra.contains(postId);
  }

  /// 检查是否拉黑某个用户
  bool isUserBlocked_Kipra(String userId) {
    return blockedUserIds_Kipra.contains(userId);
  }

  /// 检查是否举报某个帖子
  bool isPostReported_Kipra(String postId) {
    return reportedPostIds_Kipra.contains(postId);
  }

  /// 添加喜欢的帖子
  UserModel_Kipra addLikedPost_Kipra(String postId) {
    if (!likedPostIds_Kipra.contains(postId)) {
      final updatedLikedPosts = List<String>.from(likedPostIds_Kipra)..add(postId);
      return copyWith(likedPostIds_Kipra: updatedLikedPosts);
    }
    return this;
  }

  /// 移除喜欢的帖子
  UserModel_Kipra removeLikedPost_Kipra(String postId) {
    final updatedLikedPosts = List<String>.from(likedPostIds_Kipra)..remove(postId);
    return copyWith(likedPostIds_Kipra: updatedLikedPosts);
  }

  /// 拉黑用户
  UserModel_Kipra blockUser_Kipra(String userId) {
    if (!blockedUserIds_Kipra.contains(userId)) {
      final updatedBlockedUsers = List<String>.from(blockedUserIds_Kipra)..add(userId);
      return copyWith(blockedUserIds_Kipra: updatedBlockedUsers);
    }
    return this;
  }

  /// 取消拉黑用户
  UserModel_Kipra unblockUser_Kipra(String userId) {
    final updatedBlockedUsers = List<String>.from(blockedUserIds_Kipra)..remove(userId);
    return copyWith(blockedUserIds_Kipra: updatedBlockedUsers);
  }

  /// 举报帖子
  UserModel_Kipra reportPost_Kipra(String postId) {
    if (!reportedPostIds_Kipra.contains(postId)) {
      final updatedReportedPosts = List<String>.from(reportedPostIds_Kipra)..add(postId);
      return copyWith(reportedPostIds_Kipra: updatedReportedPosts);
    }
    return this;
  }

  /// 更新金币余额
  UserModel_Kipra updateCoinBalance_Kipra(int newBalance) {
    return copyWith(coinBalance_Kipra: newBalance);
  }

  /// 增加金币
  UserModel_Kipra addCoins_Kipra(int amount) {
    return copyWith(coinBalance_Kipra: coinBalance_Kipra + amount);
  }

  /// 扣除金币
  UserModel_Kipra deductCoins_Kipra(int amount) {
    final newBalance = (coinBalance_Kipra - amount).clamp(0, double.infinity).toInt();
    return copyWith(coinBalance_Kipra: newBalance);
  }

  @override
  String toString() {
    return 'UserModel_Kipra{userId: $userId_Kipra, nickname: $nickname_Kipra, coinBalance: $coinBalance_Kipra}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel_Kipra && other.userId_Kipra == userId_Kipra;
  }

  @override
  int get hashCode => userId_Kipra.hashCode;
}
