import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';
import '../../models_qwer_Kipra_84/models_export_Kipra.dart';
import '../../data_mnop_Kipra_47/sample_data_qwer_Kipra_83.dart';

/// Comment bottom sheet widget for displaying and adding comments
class CommentBottomSheet_Kipra extends StatefulWidget {
  final PostModel_Kipra post;
  final Function(PostModel_Kipra) onPostUpdated;

  const CommentBottomSheet_Kipra({
    super.key,
    required this.post,
    required this.onPostUpdated,
  });

  @override
  State<CommentBottomSheet_Kipra> createState() =>
      _CommentBottomSheetState_Kipra();
}

class _CommentBottomSheetState_Kipra extends State<CommentBottomSheet_Kipra> {
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  List<CommentModel_Kipra> _comments = [];
  List<UserModel_Kipra> _users = [];
  UserModel_Kipra? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _commentController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final users = SampleData_Kipra.getSampleUsersSync_Kipra();
      final currentUser = users.first; // First user is the logged-in user

      setState(() {
        _comments = List.from(widget.post.comments_Kipra);
        _users = users;
        _currentUser = currentUser;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addComment() async {
    if (_commentController.text.trim().isEmpty || _currentUser == null) return;

    final newComment = CommentModel_Kipra(
      commentId_Kipra: 'comment_${DateTime.now().microsecondsSinceEpoch}',
      commenterId_Kipra: _currentUser!.userId_Kipra,
      content_Kipra: _commentController.text.trim(),
      createdAt_Kipra: DateTime.now(),
      likeCount_Kipra: 0,
      isDeleted_Kipra: false,
    );

    // Update local comments
    setState(() {
      _comments.insert(0, newComment);
    });

    // Update post with new comment
    final updatedPost = widget.post.copyWith(
      comments_Kipra: _comments,
      commentCount_Kipra: _comments.length,
    );

    // Save to persistence
    await SampleData_Kipra.updatePost_Kipra(updatedPost);

    // Notify parent
    widget.onPostUpdated(updatedPost);

    // Clear input
    _commentController.clear();

    // Unfocus to hide keyboard
    _commentFocusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _commentFocusNode.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.symmetric(vertical: 12.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            // Header
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Comments (${_comments.length})',
                    style: GoogleFonts.poppins(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: AppTheme_Kipra.textPrimary,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: AppTheme_Kipra.textSecondary,
                      size: 24.sp,
                    ),
                  ),
                ],
              ),
            ),
            Divider(color: Colors.grey[200]),
            // Comments list
            SizedBox(height: 20.h),
            Expanded(
              child: _isLoading
                  ? Center(
                      child: CircularProgressIndicator(
                        color: AppTheme_Kipra.primaryColor,
                      ),
                    )
                  : _comments.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                FontAwesomeIcons.comment,
                                size: 48.sp,
                                color: Colors.grey[300],
                              ),
                              SizedBox(height: 16.h),
                              Text(
                                'No comments yet',
                                style: GoogleFonts.poppins(
                                  fontSize: 16.sp,
                                  color: AppTheme_Kipra.textSecondary,
                                ),
                              ),
                              Text(
                                'Be the first to comment!',
                                style: GoogleFonts.poppins(
                                  fontSize: 14.sp,
                                  color: AppTheme_Kipra.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          itemCount: _comments.length,
                          itemBuilder: (context, index) {
                            final comment = _comments[index];
                            final commenter = _users.firstWhere(
                              (user) =>
                                  user.userId_Kipra ==
                                  comment.commenterId_Kipra,
                              orElse: () => _users.first,
                            );
                            return _buildCommentItem(comment, commenter);
                          },
                        ),
            ),
            // Input area
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Colors.grey[200]!),
                ),
              ),
              child: Row(
                children: [
                  // Current user avatar
                  CircleAvatar(
                    radius: 20.r,
                    backgroundImage: AssetImage(_currentUser!.avatarPath_Kipra),
                  ),
                  SizedBox(width: 12.w),
                  // Input field
                  Expanded(
                    child: TextField(
                      controller: _commentController,
                      focusNode: _commentFocusNode,
                      decoration: InputDecoration(
                        hintText: 'Add a comment...',
                        hintStyle: GoogleFonts.poppins(
                          color: AppTheme_Kipra.textSecondary,
                          fontSize: 14.sp,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20.r),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20.r),
                          borderSide:
                              BorderSide(color: AppTheme_Kipra.primaryColor),
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 12.h,
                        ),
                      ),
                      maxLines: null,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) => _addComment(),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  // Send button
                  GestureDetector(
                    onTap: _addComment,
                    child: Container(
                      width: 40.w,
                      height: 40.h,
                      decoration: BoxDecoration(
                        color: AppTheme_Kipra.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        FontAwesomeIcons.paperPlane,
                        color: Colors.white,
                        size: 16.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentItem(
      CommentModel_Kipra comment, UserModel_Kipra commenter) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Commenter avatar
          CircleAvatar(
            radius: 18.r,
            backgroundImage: AssetImage(commenter.avatarPath_Kipra),
          ),
          SizedBox(width: 12.w),
          // Comment content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Commenter name and time
                Row(
                  children: [
                    Text(
                      commenter.nickname_Kipra,
                      style: GoogleFonts.poppins(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppTheme_Kipra.textPrimary,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      comment.getFormattedTime_Kipra(),
                      style: GoogleFonts.poppins(
                        fontSize: 12.sp,
                        color: AppTheme_Kipra.textSecondary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4.h),
                // Comment text
                Text(
                  comment.content_Kipra,
                  style: GoogleFonts.poppins(
                    fontSize: 14.sp,
                    color: AppTheme_Kipra.textPrimary,
                    height: 1.4,
                  ),
                ),
                SizedBox(height: 8.h),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
