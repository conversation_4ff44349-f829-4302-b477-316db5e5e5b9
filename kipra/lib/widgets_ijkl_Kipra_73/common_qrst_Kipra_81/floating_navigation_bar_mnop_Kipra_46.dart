import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';

class FloatingNavigationBar_Kipra extends StatelessWidget {
  final int currentIndex_Kipra;
  final Function(int) onTap_Kipra;

  const FloatingNavigationBar_Kipra({
    super.key,
    required this.currentIndex_Kipra,
    required this.onTap_Kipra,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 30.h,
      left: 40.w,
      right: 40.w,
      child: Container(
        height: 70.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(35.r),
          boxShadow: [
            BoxShadow(
              color: AppTheme_Kipra.shadowColor.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
            BoxShadow(
              color: AppTheme_Kipra.primaryColor.withOpacity(0.1),
              blurRadius: 30,
              offset: const Offset(0, 15),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(35.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNavItem(
                index: 0,
                icon: FontAwesomeIcons.house,
                isSelected: currentIndex_Kipra == 0,
              ),
              GestureDetector(
                onTap: () => onTap_Kipra(1),
                behavior: HitTestBehavior.opaque,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Center button with special styling
                      Container(
                        width: 48.w,
                        height: 48.h,
                        decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme_Kipra.accentColor1,
                                AppTheme_Kipra.accentColor1.withOpacity(0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(24.r),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme_Kipra.accentColor1
                                    .withOpacity(0.4),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ]),
                        child: Icon(
                          FontAwesomeIcons.plus,
                          color: Colors.white,
                          size: 22.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              _buildNavItem(
                index: 2,
                icon: FontAwesomeIcons.user,
                isSelected: currentIndex_Kipra == 2,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required bool isSelected,
    bool isCenter = false,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () => onTap_Kipra(index),
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Regular navigation items
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                width: isSelected ? 40.w : 32.w,
                height: isSelected ? 40.h : 32.h,
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppTheme_Kipra.primaryColor.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Icon(
                  icon,
                  color: isSelected
                      ? AppTheme_Kipra.primaryColor
                      : AppTheme_Kipra.textTertiary,
                  size: isSelected ? 20.sp : 18.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Alternative floating navigation bar with more modern design
class ModernFloatingNavigationBar_Kipra extends StatelessWidget {
  final int currentIndex_Kipra;
  final Function(int) onTap_Kipra;

  const ModernFloatingNavigationBar_Kipra({
    super.key,
    required this.currentIndex_Kipra,
    required this.onTap_Kipra,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 30.h,
      left: 30.w,
      right: 30.w,
      child: Container(
        height: 65.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32.5.r),
          border: Border.all(
            color: AppTheme_Kipra.borderColor.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 25,
              offset: const Offset(0, 15),
            ),
            BoxShadow(
              color: AppTheme_Kipra.primaryColor.withOpacity(0.05),
              blurRadius: 35,
              offset: const Offset(0, 20),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(32.5.r),
          child: Stack(
            children: [
              // Background indicator
              AnimatedPositioned(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOutCubic,
                left: _getIndicatorPosition(),
                top: 8.h,
                child: Container(
                  width: 50.w,
                  height: 49.h,
                  decoration: BoxDecoration(
                    color: AppTheme_Kipra.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(25.r),
                  ),
                ),
              ),
              // Navigation items
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildModernNavItem(
                    index: 0,
                    icon: FontAwesomeIcons.house,
                    isSelected: currentIndex_Kipra == 0,
                  ),
                  _buildModernNavItem(
                    index: 1,
                    icon: FontAwesomeIcons.plus,
                    isSelected: currentIndex_Kipra == 1,
                  ),
                  _buildModernNavItem(
                    index: 2,
                    icon: FontAwesomeIcons.store,
                    isSelected: currentIndex_Kipra == 2,
                  ),
                  _buildModernNavItem(
                    index: 3,
                    icon: FontAwesomeIcons.user,
                    isSelected: currentIndex_Kipra == 3,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _getIndicatorPosition() {
    // Calculate position based on current index
    final screenWidth = 1.sw - 60.w; // Total width minus margins
    final itemWidth = screenWidth / 4;
    return 15.w + (currentIndex_Kipra * itemWidth) + (itemWidth - 50.w) / 2;
  }

  Widget _buildModernNavItem({
    required int index,
    required IconData icon,
    required bool isSelected,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () => onTap_Kipra(index),
        behavior: HitTestBehavior.opaque,
        child: Container(
          height: 65.h,
          child: Center(
            child: AnimatedScale(
              duration: const Duration(milliseconds: 200),
              scale: isSelected ? 1.1 : 1.0,
              child: Icon(
                icon,
                color: isSelected
                    ? AppTheme_Kipra.primaryColor
                    : AppTheme_Kipra.textTertiary,
                size: isSelected ? 22.sp : 20.sp,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Glassmorphism style floating navigation bar
class GlassmorphismNavigationBar_Kipra extends StatelessWidget {
  final int currentIndex_Kipra;
  final Function(int) onTap_Kipra;

  const GlassmorphismNavigationBar_Kipra({
    super.key,
    required this.currentIndex_Kipra,
    required this.onTap_Kipra,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 30.h,
      left: 25.w,
      right: 25.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30.r),
        child: Container(
          height: 68.h,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(30.r),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 30,
                offset: const Offset(0, 20),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildGlassNavItem(
                index: 0,
                icon: FontAwesomeIcons.house,
                isSelected: currentIndex_Kipra == 0,
              ),
              _buildGlassNavItem(
                index: 1,
                icon: FontAwesomeIcons.plus,
                isSelected: currentIndex_Kipra == 1,
              ),
              _buildGlassNavItem(
                index: 2,
                icon: FontAwesomeIcons.store,
                isSelected: currentIndex_Kipra == 2,
              ),
              _buildGlassNavItem(
                index: 3,
                icon: FontAwesomeIcons.user,
                isSelected: currentIndex_Kipra == 3,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGlassNavItem({
    required int index,
    required IconData icon,
    required bool isSelected,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () => onTap_Kipra(index),
        behavior: HitTestBehavior.opaque,
        child: Container(
          height: 68.h,
          child: Center(
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              width: isSelected ? 45.w : 35.w,
              height: isSelected ? 45.h : 35.h,
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme_Kipra.primaryColor.withOpacity(0.15)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(22.5.r),
              ),
              child: Icon(
                icon,
                color: isSelected
                    ? AppTheme_Kipra.primaryColor
                    : AppTheme_Kipra.textTertiary,
                size: isSelected ? 20.sp : 18.sp,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
