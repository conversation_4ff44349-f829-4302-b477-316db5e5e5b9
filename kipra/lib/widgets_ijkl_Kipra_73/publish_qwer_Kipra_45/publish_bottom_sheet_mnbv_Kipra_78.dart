import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';
import '../../core_asdf_Kipra_12/theme_wxyz_Kipra_28/app_theme_Kipra.dart';
import '../../models_qwer_Kipra_84/models_export_Kipra.dart';
import '../../services_qwer_Kipra_45/data_persistence_service_mnbv_Kipra_78.dart';

/// Publish bottom sheet for creating volleyball posts
class PublishBottomSheet_Kipra extends StatefulWidget {
  final VoidCallback? onPostPublished;

  const PublishBottomSheet_Kipra({
    super.key,
    this.onPostPublished,
  });

  @override
  State<PublishBottomSheet_Kipra> createState() =>
      _PublishBottomSheetState_Kipra();
}

class _PublishBottomSheetState_Kipra extends State<PublishBottomSheet_Kipra>
    with TickerProviderStateMixin {
  final TextEditingController _titleController_Kipra = TextEditingController();
  final FocusNode _titleFocusNode_Kipra = FocusNode();

  String? _selectedVideoPath_Kipra;
  VideoPlayerController? _videoController_Kipra;
  bool _isRecording_Kipra = false;
  bool _isUploading_Kipra = false;
  bool _isPublishing_Kipra = false;
  bool _isVideoPlaying_Kipra = false;

  final List<String> _selectedTags_Kipra = [];
  final ImagePicker _picker_Kipra = ImagePicker();

  late AnimationController _slideController_Kipra;
  late Animation<Offset> _slideAnimation_Kipra;

  // Available volleyball tags
  static const List<String> _availableTags_Kipra = [
    'spike',
    'serve',
    'block',
    'dig',
    'set',
    'training',
    'technique',
    'tournament',
    'tips',
    'match',
  ];

  @override
  void initState() {
    super.initState();
    _slideController_Kipra = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _slideAnimation_Kipra = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController_Kipra,
      curve: Curves.easeOutCubic,
    ));
    _slideController_Kipra.forward();
  }

  @override
  void dispose() {
    _titleController_Kipra.dispose();
    _titleFocusNode_Kipra.dispose();
    _videoController_Kipra?.dispose();
    _slideController_Kipra.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: SlideTransition(
        position: _slideAnimation_Kipra,
        child: Container(
          height: MediaQuery.of(context).size.height * 0.9,
          decoration: BoxDecoration(
            color: AppTheme_Kipra.backgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(25.r)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20.h),
                      _buildVideoSection(),
                      SizedBox(height: 25.h),
                      _buildInputSection(),
                      SizedBox(height: 25.h),
                      _buildTagsSection(),
                      SizedBox(height: 30.h),
                      _buildPublishButton(),
                      SizedBox(height: 40.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(25.r)),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              width: 40.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: AppTheme_Kipra.backgroundColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                FontAwesomeIcons.xmark,
                color: AppTheme_Kipra.textSecondary,
                size: 18.sp,
              ),
            ),
          ),
          SizedBox(width: 15.w),
          Expanded(
            child: Text(
              'Create New Post',
              style: GoogleFonts.poppins(
                fontSize: 20.sp,
                color: AppTheme_Kipra.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Container(
            width: 8.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: AppTheme_Kipra.primaryColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoSection() {
    return Container(
      width: double.infinity,
      height: 280.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppTheme_Kipra.shadowColor,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _selectedVideoPath_Kipra == null
          ? _buildVideoSelectionArea()
          : _buildVideoPreview(),
    );
  }

  Widget _buildVideoSelectionArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 80.w,
          height: 80.h,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme_Kipra.primaryColor,
                AppTheme_Kipra.primaryColor.withOpacity(0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(25.r),
          ),
          child: Icon(
            FontAwesomeIcons.video,
            color: Colors.white,
            size: 32.sp,
          ),
        ),
        SizedBox(height: 20.h),
        Text(
          'Add Your Volleyball Video',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Share your skills, techniques, or matches',
          style: GoogleFonts.poppins(
            fontSize: 14.sp,
            color: AppTheme_Kipra.textSecondary,
            fontWeight: FontWeight.w400,
          ),
        ),
        SizedBox(height: 25.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: FontAwesomeIcons.camera,
              color: AppTheme_Kipra.accentColor1,
              onTap: _recordVideo,
              isLoading: _isRecording_Kipra,
            ),
            _buildActionButton(
              icon: FontAwesomeIcons.photoFilm,
              color: AppTheme_Kipra.accentColor2,
              onTap: _selectFromGallery,
              isLoading: _isUploading_Kipra,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isLoading = false,
  }) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: 120.w,
        height: 50.h,
        decoration: BoxDecoration(
          color: color.withOpacity(0.2),
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: isLoading
            ? Center(
                child: SizedBox(
                  width: 20.w,
                  height: 20.h,
                  child: CircularProgressIndicator(
                    color: color,
                    strokeWidth: 2,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: color, size: 18.sp),
                ],
              ),
      ),
    );
  }

  Widget _buildVideoPreview() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(20.r),
          child: _videoController_Kipra != null &&
                  _videoController_Kipra!.value.isInitialized
              ? AspectRatio(
                  aspectRatio: _videoController_Kipra!.value.aspectRatio,
                  child: VideoPlayer(_videoController_Kipra!),
                )
              : Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.grey[300],
                  child: Center(
                    child: CircularProgressIndicator(
                      color: AppTheme_Kipra.primaryColor,
                    ),
                  ),
                ),
        ),
        // Video controls overlay
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.3),
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Top controls
                Padding(
                  padding: EdgeInsets.all(15.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      GestureDetector(
                        onTap: _removeVideo,
                        child: Container(
                          width: 35.w,
                          height: 35.h,
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          child: Icon(
                            FontAwesomeIcons.trash,
                            color: Colors.white,
                            size: 16.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Play button
                Center(
                  child: GestureDetector(
                    onTap: _toggleVideoPlayback,
                    child: Container(
                      width: 60.w,
                      height: 60.h,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(30.r),
                      ),
                      child: Icon(
                        _isVideoPlaying_Kipra
                            ? FontAwesomeIcons.pause
                            : FontAwesomeIcons.play,
                        color: Colors.white,
                        size: 24.sp,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 60.h), // Bottom spacing
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Post Details',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 15.h),
        // Title input
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(15.r),
            boxShadow: [
              BoxShadow(
                color: AppTheme_Kipra.shadowColor,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _titleController_Kipra,
            focusNode: _titleFocusNode_Kipra,
            style: GoogleFonts.poppins(
              fontSize: 16.sp,
              color: AppTheme_Kipra.textPrimary,
            ),
            decoration: InputDecoration(
              hintText: 'Enter post title...',
              hintStyle: GoogleFonts.poppins(
                fontSize: 16.sp,
                color: AppTheme_Kipra.textSecondary,
              ),
              prefixIcon: Icon(
                FontAwesomeIcons.heading,
                color: AppTheme_Kipra.primaryColor,
                size: 18.sp,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 20.w,
                vertical: 16.h,
              ),
            ),
            maxLength: 100,
            buildCounter: (context,
                {required currentLength, required isFocused, maxLength}) {
              return Text(
                '$currentLength/$maxLength',
                style: GoogleFonts.poppins(
                  fontSize: 12.sp,
                  color: AppTheme_Kipra.textSecondary,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: GoogleFonts.poppins(
            fontSize: 18.sp,
            color: AppTheme_Kipra.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Select up to 4 tags to categorize your post',
          style: GoogleFonts.poppins(
            fontSize: 12.sp,
            color: AppTheme_Kipra.textSecondary,
          ),
        ),
        SizedBox(height: 15.h),
        Wrap(
          spacing: 10.w,
          runSpacing: 10.h,
          children: _availableTags_Kipra.map((tag) {
            final isSelected = _selectedTags_Kipra.contains(tag);
            return GestureDetector(
              onTap: () => _toggleTag(tag),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 8.h,
                ),
                decoration: BoxDecoration(
                  color:
                      isSelected ? AppTheme_Kipra.primaryColor : Colors.white,
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme_Kipra.primaryColor
                        : AppTheme_Kipra.primaryColor.withOpacity(0.3),
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: AppTheme_Kipra.primaryColor.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : [
                          BoxShadow(
                            color: AppTheme_Kipra.shadowColor,
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                ),
                child: Text(
                  '#$tag',
                  style: GoogleFonts.poppins(
                    fontSize: 13.sp,
                    color:
                        isSelected ? Colors.white : AppTheme_Kipra.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPublishButton() {
    final canPublish = _selectedVideoPath_Kipra != null &&
        _titleController_Kipra.text.trim().isNotEmpty &&
        !_isPublishing_Kipra;

    return Container(
      width: double.infinity,
      height: 55.h,
      decoration: BoxDecoration(
        gradient: canPublish
            ? LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  AppTheme_Kipra.primaryColor,
                  AppTheme_Kipra.primaryColor.withOpacity(0.8),
                ],
              )
            : null,
        color: canPublish ? null : Colors.grey[300],
        borderRadius: BorderRadius.circular(18.r),
        boxShadow: canPublish
            ? [
                BoxShadow(
                  color: AppTheme_Kipra.primaryColor.withOpacity(0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canPublish ? _publishPost : null,
          borderRadius: BorderRadius.circular(18.r),
          child: Center(
            child: _isPublishing_Kipra
                ? SizedBox(
                    width: 24.w,
                    height: 24.h,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FontAwesomeIcons.paperPlane,
                        color: canPublish ? Colors.white : Colors.grey[600],
                        size: 18.sp,
                      ),
                      SizedBox(width: 10.w),
                      Text(
                        'Publish Post',
                        style: GoogleFonts.poppins(
                          fontSize: 16.sp,
                          color: canPublish ? Colors.white : Colors.grey[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  // Video recording functionality
  Future<void> _recordVideo() async {
    setState(() {
      _isRecording_Kipra = true;
    });

    try {
      final XFile? video = await _picker_Kipra.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 3),
      );

      if (video != null) {
        await _initializeVideoController(video.path);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to record video: $e');
    } finally {
      setState(() {
        _isRecording_Kipra = false;
      });
    }
  }

  // Gallery selection functionality
  Future<void> _selectFromGallery() async {
    setState(() {
      _isUploading_Kipra = true;
    });

    try {
      final XFile? video = await _picker_Kipra.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 3),
      );

      if (video != null) {
        await _initializeVideoController(video.path);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to select video: $e');
    } finally {
      setState(() {
        _isUploading_Kipra = false;
      });
    }
  }

  // Initialize video controller
  Future<void> _initializeVideoController(String videoPath) async {
    _videoController_Kipra?.dispose();

    setState(() {
      _selectedVideoPath_Kipra = videoPath;
      _videoController_Kipra = VideoPlayerController.file(File(videoPath));
    });

    try {
      await _videoController_Kipra!.initialize();
      setState(() {});
    } catch (e) {
      _showErrorSnackBar('Failed to load video: $e');
    }
  }

  // Remove selected video
  void _removeVideo() {
    _videoController_Kipra?.dispose();
    setState(() {
      _selectedVideoPath_Kipra = null;
      _videoController_Kipra = null;
      _isVideoPlaying_Kipra = false;
    });
  }

  // Toggle video playback
  void _toggleVideoPlayback() {
    if (_videoController_Kipra != null) {
      setState(() {
        if (_isVideoPlaying_Kipra) {
          _videoController_Kipra!.pause();
          _isVideoPlaying_Kipra = false;
        } else {
          _videoController_Kipra!.play();
          _isVideoPlaying_Kipra = true;
        }
      });
    }
  }

  // Toggle tag selection
  void _toggleTag(String tag) {
    setState(() {
      if (_selectedTags_Kipra.contains(tag)) {
        _selectedTags_Kipra.remove(tag);
      } else if (_selectedTags_Kipra.length < 4) {
        _selectedTags_Kipra.add(tag);
      }
    });
  }

  // Publish post functionality
  Future<void> _publishPost() async {
    if (_selectedVideoPath_Kipra == null ||
        _titleController_Kipra.text.trim().isEmpty) {
      _showErrorSnackBar('Please add a video and title');
      return;
    }

    setState(() {
      _isPublishing_Kipra = true;
    });

    try {
      // Get current user
      final currentUser =
          await DataPersistenceService_Kipra.getCurrentUser_Kipra();
      if (currentUser == null) {
        _showErrorSnackBar('User not found');
        return;
      }

      // Create new post
      final now = DateTime.now();
      final newPost = PostModel_Kipra(
        postId_Kipra: now.microsecondsSinceEpoch.toString(),
        authorId_Kipra: currentUser.userId_Kipra,
        videoPath_Kipra: _selectedVideoPath_Kipra!,
        imagePaths_Kipra: const [],
        title_Kipra: _titleController_Kipra.text.trim(),
        content_Kipra: '', // Content field is optional
        tags_Kipra: _selectedTags_Kipra,
        likeCount_Kipra: 0,
        commentCount_Kipra: 0,
        viewCount_Kipra: 0,
        createdAt_Kipra: now,
        updatedAt_Kipra: now,
        postType_Kipra: 'video',
        isPinned_Kipra: false,
        isDeleted_Kipra: false,
        status_Kipra: 'published',
        comments_Kipra: const [],
        location_Kipra: null,
      );

      // Save post to persistence
      final posts = await DataPersistenceService_Kipra.loadPosts_Kipra();
      posts.insert(0, newPost); // Add to beginning of list
      await DataPersistenceService_Kipra.savePosts_Kipra(posts);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Post published successfully!',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
            ),
          ),
        );
        Navigator.pop(context);

        // Notify parent to refresh data
        widget.onPostPublished?.call();
      }
    } catch (e) {
      _showErrorSnackBar('Failed to publish post: $e');
    } finally {
      setState(() {
        _isPublishing_Kipra = false;
      });
    }
  }

  // Show error snackbar
  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: GoogleFonts.poppins(color: Colors.white),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.r),
          ),
        ),
      );
    }
  }
}
