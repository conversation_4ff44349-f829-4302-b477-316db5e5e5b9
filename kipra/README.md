# Kipra - Volleyball First Pass Community Hub

Kipra is a specialized social platform for volleyball first pass enthusiasts, focusing on skill improvement, practice sharing, and team collaboration atmosphere.

## Features

- **Teammate Linker**: Match with players of similar skill levels
- **Drill Exchange**: Browse and share popular community exercises
- **Highlight Zone**: Post and celebrate clean first passes
- **Training Tracker**: Monitor practice frequency and progress

## Tech Stack

- **Framework**: Flutter
- **State Management**: Riverpod + MVC Architecture
- **Data Storage**: Local data persistence (SharedPreferences + Local files)
- **UI**: Material Design 3 + Custom theme
- **Responsive Design**: flutter_screenutil

## Project Structure

The project follows a specific naming convention with random letter combinations and _Kipra suffix:

```
lib/
├── core_xkqp_Kipra_42/             # Core modules
├── models_ytzm_Kipra_17/           # Data models
├── providers_abcn_Kipra_89/        # Riverpod state management
├── controllers_pqrs_Kipra_35/      # MVC controllers
├── views_efgh_Kipra_61/            # Page views
├── widgets_ijkl_Kipra_73/          # Common widgets
└── main.dart                       # App entry point
```

## Getting Started

1. Install dependencies:
```bash
flutter pub get
```

2. Run the app:
```bash
flutter run
```

## Theme Colors

- **Primary**: Court Blue (#194B7B)
- **Secondary**: Spike Coral (#FF6E5C)
- **Tertiary**: Serve Cream (#FFE8C9)
- **Background**: Net White (#F9FAFB)
- **Accent**: Volley Grass (#B0E4AD)

## Development Guidelines

- All text content must be in English
- Use GestureDetector for input fields to handle keyboard dismissal
- Follow MVC architecture with Riverpod for state management
- Keep functionality simple and focused
- Use local data processing only (no network requests)

For detailed technical documentation, see `docs/MD/技术架构.md`.
